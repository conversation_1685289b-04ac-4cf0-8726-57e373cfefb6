const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Subscription = require('../models/Subscription');
const Permission = require('../models/Permission');
const {
  asyncHandler,
  createNotFoundError,
  createValidationError,
  createDatabaseError,
  createConflictError,
  formatErrorResponse
} = require('../utils/errorHandler');
const QuotaMiddleware = require('../middleware/quotaMiddleware');
const { getSystemStats: getSystemMonitorStats } = require('../services/systemMonitor');
const subscriptionMonitor = require('../services/subscriptionMonitor');

// Middleware to check if user is admin
const requireAdmin = Permission.requireRole('admin');

// Admin dashboard
router.get('/dashboard', requireAdmin, asyncHandler(async (req, res) => {
  const users = await User.findAll(10, 0);
  const plans = await Subscription.getAllPlansWithSubscribers();

  // Get system statistics
  const stats = await getSystemStats();

  // Get system monitoring stats
  const systemStats = await getSystemMonitorStats();

  res.render('admin/dashboard', {
    title: 'Admin Dashboard',
    active: 'admin-dashboard',
    users,
    plans,
    stats,
    systemStats
  });
}));

// User management
router.get('/users', requireAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';

    // Get users and total count with search
    const [users, totalUsers] = await Promise.all([
      User.findAllWithTrialInfo(limit, offset, search),
      User.countAll(search)
    ]);

    const totalPages = Math.ceil(totalUsers / limit);

    const roles = await Permission.getAllRoles();
    const plans = await Subscription.getAllPlans();
    const stats = await getSystemStats();

    res.render('admin/users', {
      title: 'User Management',
      active: 'admin-users',
      users,
      roles,
      plans,
      stats,
      currentPage: page,
      totalPages,
      totalUsers,
      limit,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
      nextPage: page + 1,
      prevPage: page - 1,
      search: search
    });
  } catch (error) {
    console.error('User management error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load user management',
      error: error
    });
  }
});

// Update user role
router.post('/users/:userId/role', requireAdmin, asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { role } = req.body;

  if (!userId) {
    throw createValidationError('User ID is required', null, 'userId');
  }

  if (!role) {
    throw createValidationError('Role is required', null, 'role');
  }

  const validRoles = ['user', 'admin'];
  if (!validRoles.includes(role)) {
    throw createValidationError(`Role must be one of: ${validRoles.join(', ')}`, { validRoles }, 'role');
  }

  // Check if user exists
  const user = await User.findById(userId);
  if (!user) {
    throw createNotFoundError('User', userId);
  }

  await User.updateRole(userId, role);

  res.json({
    success: true,
    message: 'User role updated successfully',
    data: { userId, role }
  });
}));

// Update user plan
router.post('/users/:userId/plan', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { planId, customEndDate, useProrated } = req.body;

    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Get current subscription
    const currentSubscription = await Subscription.getUserSubscription(userId);

    let endDate;
    if (customEndDate) {
      // Use custom end date if provided
      endDate = new Date(customEndDate).toISOString();
    } else if (currentSubscription && useProrated) {
      // Keep current end date for prorated upgrade
      endDate = currentSubscription.end_date;
    } else {
      // Default: 30 days from now
      const newEndDate = new Date();
      newEndDate.setDate(newEndDate.getDate() + 30);
      endDate = newEndDate.toISOString();
    }

    // Update user plan
    await User.updatePlan(userId, plan.name, plan.max_streaming_slots, plan.max_storage_gb);

    // Create or update subscription
    if (currentSubscription) {
      // Mark current subscription as upgraded
      await Subscription.updateSubscriptionStatus(currentSubscription.id, 'upgraded');
    }

    // Create new subscription
    const subscription = await Subscription.createSubscription({
      user_id: userId,
      plan_id: planId,
      status: 'active',
      end_date: endDate,
      payment_method: 'admin_manual',
      payment_id: null
    });

    res.json({
      success: true,
      message: 'User plan updated successfully',
      subscription,
      plan
    });
  } catch (error) {
    console.error('Update user plan error:', error);
    res.status(500).json({ error: 'Failed to update user plan' });
  }
});

// Toggle user active status
router.post('/users/toggle-status', requireAdmin, async (req, res) => {
  try {
    const { userId, isActive } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    await User.updateActiveStatus(userId, isActive);

    res.json({ success: true, message: 'User status updated successfully' });
  } catch (error) {
    console.error('Toggle user status error:', error);
    res.status(500).json({ error: 'Failed to update user status' });
  }
});

// Subscription management page
router.get('/subscriptions', requireAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    // Get all subscriptions with user and plan details
    const [subscriptions, totalSubscriptions] = await Promise.all([
      Subscription.getAllSubscriptionsWithDetails(limit, offset),
      Subscription.countAllSubscriptions()
    ]);

    const totalPages = Math.ceil(totalSubscriptions / limit);
    const plans = await Subscription.getAllPlans();

    res.render('admin/subscriptions', {
      title: 'Subscription Management',
      active: 'admin-subscriptions',
      subscriptions,
      plans,
      currentPage: page,
      totalPages,
      totalSubscriptions,
      limit,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
      nextPage: page + 1,
      prevPage: page - 1
    });
  } catch (error) {
    console.error('Subscription management error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load subscription management',
      error: error
    });
  }
});

// Subscription plans management
router.get('/plans', requireAdmin, async (req, res) => {
  try {
    const plans = await Subscription.getAllPlansWithSubscribers();

    res.render('admin/plans', {
      title: 'Subscription Plans',
      active: 'admin-plans',
      plans
    });
  } catch (error) {
    console.error('Plans management error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load plans management',
      error: error
    });
  }
});

// Create new subscription plan
router.post('/plans/create', requireAdmin, async (req, res) => {
  try {
    const { name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features } = req.body;

    if (!name || price === undefined || price === null || price === '') {
      return res.status(400).json({ error: 'Plan name and price are required' });
    }

    // Convert price to number and validate
    const numericPrice = parseFloat(price);
    if (isNaN(numericPrice) || numericPrice < 0) {
      return res.status(400).json({ error: 'Price must be a valid number (0 or greater)' });
    }

    // Check if plan name already exists (only check active plans)
    const existingPlan = await Subscription.getPlanByName(name.trim());
    console.log(`Checking plan name "${name.trim()}" - Found existing:`, existingPlan ? existingPlan.id : 'none');
    if (existingPlan) {
      return res.status(400).json({
        error: 'A plan with this name already exists',
        details: `Plan "${name}" already exists with ID: ${existingPlan.id}`
      });
    }

    // Validate and convert streaming slots
    const numericSlots = max_streaming_slots !== undefined && max_streaming_slots !== '' ? parseInt(max_streaming_slots) : 1;
    if (isNaN(numericSlots) || numericSlots < -1) {
      return res.status(400).json({ error: 'Max streaming slots must be a valid number (-1 for unlimited, 0 or greater)' });
    }

    // Validate and convert storage (handle both old format and new format)
    let numericStorage;
    if (req.body.max_storage_value !== undefined && req.body.storage_unit !== undefined) {
      // New format with unit selection
      const storageValue = parseFloat(req.body.max_storage_value);
      const storageUnit = req.body.storage_unit;

      if (isNaN(storageValue) || storageValue < 0) {
        return res.status(400).json({ error: 'Max storage must be a valid number (0 or greater)' });
      }

      numericStorage = storageUnit === 'mb' ? storageValue / 1024 : storageValue;
    } else {
      // Old format (GB only)
      numericStorage = max_storage_gb !== undefined && max_storage_gb !== '' ? parseFloat(max_storage_gb) : 5;
      if (isNaN(numericStorage) || numericStorage < 0) {
        return res.status(400).json({ error: 'Max storage must be a valid number (0 or greater)' });
      }
    }

    // Sanitize features array to prevent XSS
    const sanitizedFeatures = Array.isArray(features) ?
      features
        .filter(f => typeof f === 'string' && f.trim().length > 0)
        .map(f => f.trim().substring(0, 100))
        .filter(f => f.length > 0)
        .slice(0, 20) // Limit to 20 features max
      : [];

    const planData = {
      name,
      price: numericPrice,
      currency: currency || 'USD',
      billing_period: billing_period || 'monthly',
      max_streaming_slots: numericSlots,
      max_storage_gb: numericStorage,
      features: sanitizedFeatures
    };

    const plan = await Subscription.createPlan(planData);

    res.json({ success: true, message: 'Plan created successfully', plan });
  } catch (error) {
    console.error('Create plan error:', error);
    res.status(500).json({ error: 'Failed to create plan' });
  }
});

// Update subscription plan
router.post('/plans/:planId/edit', requireAdmin, async (req, res) => {
  try {
    const { planId } = req.params;
    const { name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features } = req.body;

    if (!name || price === undefined || price === null || price === '') {
      return res.status(400).json({ error: 'Plan name and price are required' });
    }

    // Convert price to number and validate
    const numericPrice = parseFloat(price);
    if (isNaN(numericPrice) || numericPrice < 0) {
      return res.status(400).json({ error: 'Price must be a valid number (0 or greater)' });
    }

    // Check if plan name already exists (excluding current plan, only check active plans)
    const existingPlan = await Subscription.getPlanByName(name.trim());
    console.log(`Editing plan ${planId} - Checking name "${name.trim()}" - Found existing:`, existingPlan ? existingPlan.id : 'none');
    if (existingPlan && existingPlan.id !== planId) {
      return res.status(400).json({
        error: 'A plan with this name already exists',
        details: `Plan "${name}" already exists with ID: ${existingPlan.id}. Current plan ID: ${planId}`
      });
    }

    // Validate and convert streaming slots
    const numericSlots = max_streaming_slots !== undefined && max_streaming_slots !== '' ? parseInt(max_streaming_slots) : 1;
    if (isNaN(numericSlots) || numericSlots < -1) {
      return res.status(400).json({ error: 'Max streaming slots must be a valid number (-1 for unlimited, 0 or greater)' });
    }

    // Validate and convert storage (handle both old format and new format)
    let numericStorage;
    if (req.body.max_storage_value !== undefined && req.body.storage_unit !== undefined) {
      // New format with unit selection
      const storageValue = parseFloat(req.body.max_storage_value);
      const storageUnit = req.body.storage_unit;

      if (isNaN(storageValue) || storageValue < 0) {
        return res.status(400).json({ error: 'Max storage must be a valid number (0 or greater)' });
      }

      numericStorage = storageUnit === 'mb' ? storageValue / 1024 : storageValue;
    } else {
      // Old format (GB only)
      numericStorage = max_storage_gb !== undefined && max_storage_gb !== '' ? parseFloat(max_storage_gb) : 5;
      if (isNaN(numericStorage) || numericStorage < 0) {
        return res.status(400).json({ error: 'Max storage must be a valid number (0 or greater)' });
      }
    }

    // Sanitize features array to prevent XSS
    const sanitizedFeatures = Array.isArray(features) ?
      features
        .filter(f => typeof f === 'string' && f.trim().length > 0)
        .map(f => f.trim().substring(0, 100))
        .filter(f => f.length > 0)
        .slice(0, 20) // Limit to 20 features max
      : [];

    const planData = {
      name,
      price: numericPrice,
      currency: currency || 'USD',
      billing_period: billing_period || 'monthly',
      max_streaming_slots: numericSlots,
      max_storage_gb: numericStorage,
      features: sanitizedFeatures
    };

    const plan = await Subscription.updatePlan(planId, planData);

    res.json({ success: true, message: 'Plan updated successfully', plan });
  } catch (error) {
    console.error('Update plan error:', error);
    res.status(500).json({ error: 'Failed to update plan' });
  }
});

// Delete subscription plan
router.post('/plans/:planId/delete', requireAdmin, async (req, res) => {
  try {
    const { planId } = req.params;

    // Check if plan exists
    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    // Don't allow deleting the free plan
    if (plan.name.toLowerCase() === 'free' || plan.name.toLowerCase().includes('free')) {
      return res.status(400).json({ error: 'Cannot delete the free plan' });
    }

    await Subscription.deletePlan(planId);

    res.json({ success: true, message: 'Plan deleted successfully' });
  } catch (error) {
    console.error('Delete plan error:', error);
    res.status(500).json({ error: 'Failed to delete plan' });
  }
});

// Get plan details
router.get('/plans/:planId', requireAdmin, async (req, res) => {
  try {
    const { planId } = req.params;

    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    res.json({ success: true, plan });
  } catch (error) {
    console.error('Get plan details error:', error);
    res.status(500).json({ error: 'Failed to get plan details' });
  }
});

// Get plan subscribers
router.get('/plans/:planId/subscribers', requireAdmin, async (req, res) => {
  try {
    const { planId } = req.params;

    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    const subscribers = await Subscription.getPlanSubscribers(planId, plan.name);

    res.json({
      success: true,
      plan,
      subscribers
    });
  } catch (error) {
    console.error('Get plan subscribers error:', error);
    res.status(500).json({ error: 'Failed to get plan subscribers' });
  }
});

// Create manual subscription
router.post('/subscriptions/create', requireAdmin, async (req, res) => {
  try {
    const { userId, planId, startDate, endDate, paymentMethod = 'admin_manual' } = req.body;

    if (!userId || !planId || !endDate) {
      return res.status(400).json({ error: 'User ID, Plan ID, and End Date are required' });
    }

    // Validate user exists - try both ID and username
    let user = await User.findById(userId);
    if (!user) {
      // If not found by ID, try by username
      user = await User.findByUsername(userId);
    }
    if (!user) {
      return res.status(404).json({ error: 'User not found. Please check the User ID or username.' });
    }

    // Validate plan exists
    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    // Ensure non-Preview plans have end_date
    if (plan.name !== 'Preview' && !endDate) {
      return res.status(400).json({
        error: 'End date is required for all paid plans. Only Preview plan can be unlimited.'
      });
    }

    // Validate dates
    const start = startDate ? new Date(startDate) : new Date();
    const end = new Date(endDate);

    if (end <= start) {
      return res.status(400).json({ error: 'End date must be after start date' });
    }

    // Get current subscription and mark as upgraded if exists
    const currentSubscription = await Subscription.getUserSubscription(user.id);
    if (currentSubscription) {
      await Subscription.updateSubscriptionStatus(currentSubscription.id, 'upgraded');
    }

    // Create new subscription
    const subscription = await Subscription.createSubscription({
      user_id: user.id,
      plan_id: planId,
      status: 'active',
      start_date: start.toISOString(),
      end_date: end.toISOString(),
      payment_method: paymentMethod,
      payment_id: null
    });

    // Update user plan
    await User.updatePlan(user.id, plan.name, plan.max_streaming_slots, plan.max_storage_gb);

    res.json({
      success: true,
      message: 'Subscription created successfully',
      subscription,
      plan
    });
  } catch (error) {
    console.error('Create subscription error:', error);
    res.status(500).json({ error: 'Failed to create subscription' });
  }
});

// Extend subscription
router.post('/subscriptions/:subscriptionId/extend', requireAdmin, async (req, res) => {
  try {
    const { subscriptionId } = req.params;
    const { extensionDays, newEndDate } = req.body;

    if (!extensionDays && !newEndDate) {
      return res.status(400).json({ error: 'Extension days or new end date is required' });
    }

    // Get subscription
    const subscription = await Subscription.getSubscriptionById(subscriptionId);
    if (!subscription) {
      return res.status(404).json({ error: 'Subscription not found' });
    }

    let endDate;
    if (newEndDate) {
      endDate = new Date(newEndDate);
    } else {
      endDate = new Date(subscription.end_date);
      endDate.setDate(endDate.getDate() + parseInt(extensionDays));
    }

    // Update subscription end date
    await Subscription.updateSubscriptionEndDate(subscriptionId, endDate.toISOString());

    res.json({
      success: true,
      message: 'Subscription extended successfully',
      newEndDate: endDate.toISOString()
    });
  } catch (error) {
    console.error('Extend subscription error:', error);
    res.status(500).json({ error: 'Failed to extend subscription' });
  }
});

// Cancel subscription
router.post('/subscriptions/:subscriptionId/cancel', requireAdmin, async (req, res) => {
  try {
    const { subscriptionId } = req.params;

    // Get subscription
    const subscription = await Subscription.getSubscriptionById(subscriptionId);
    if (!subscription) {
      return res.status(404).json({ error: 'Subscription not found' });
    }

    // Update subscription status to cancelled
    await Subscription.updateSubscriptionStatus(subscriptionId, 'cancelled');

    // Set end_date to current time to make it immediately expired
    const currentTime = new Date().toISOString();
    await Subscription.updateSubscriptionEndDate(subscriptionId, currentTime);

    // Handle admin cancellation (downgrade user but keep subscription record for display)
    const success = await Subscription.handleAdminCancellation(subscription.user_id);

    if (success) {
      console.log(`✅ Admin cancelled subscription ${subscriptionId} for user ${subscription.user_id} - user downgraded to Preview plan`);
    } else {
      console.warn(`⚠️ Admin cancelled subscription ${subscriptionId} but failed to fully process cancellation for user ${subscription.user_id}`);
    }

    res.json({
      success: true,
      message: 'Subscription cancelled successfully and user downgraded to Preview plan'
    });
  } catch (error) {
    console.error('Cancel subscription error:', error);
    res.status(500).json({ error: 'Failed to cancel subscription' });
  }
});

// Get user subscription history
router.get('/users/:userId/subscriptions', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    // Validate user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const subscriptions = await Subscription.getUserSubscriptionHistory(userId);

    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        plan_type: user.plan_type
      },
      subscriptions
    });
  } catch (error) {
    console.error('Get user subscriptions error:', error);
    res.status(500).json({ error: 'Failed to get user subscriptions' });
  }
});

// Bulk operations for plans
router.post('/plans/bulk-action', requireAdmin, async (req, res) => {
  try {
    const { action, planIds } = req.body;

    if (!action || !Array.isArray(planIds) || planIds.length === 0) {
      return res.status(400).json({ error: 'Action and plan IDs are required' });
    }

    let results = [];

    switch (action) {
      case 'delete':
        for (const planId of planIds) {
          try {
            const plan = await Subscription.getPlanById(planId);
            if (plan && plan.name.toLowerCase() !== 'free' && !plan.name.toLowerCase().includes('free')) {
              await Subscription.deletePlan(planId);
              results.push({ planId, success: true });
            } else {
              results.push({ planId, success: false, error: 'Cannot delete free plan' });
            }
          } catch (error) {
            results.push({ planId, success: false, error: error.message });
          }
        }
        break;

      case 'activate':
        for (const planId of planIds) {
          try {
            await Subscription.updatePlanStatus(planId, true);
            results.push({ planId, success: true });
          } catch (error) {
            results.push({ planId, success: false, error: error.message });
          }
        }
        break;

      case 'deactivate':
        for (const planId of planIds) {
          try {
            await Subscription.updatePlanStatus(planId, false);
            results.push({ planId, success: true });
          } catch (error) {
            results.push({ planId, success: false, error: error.message });
          }
        }
        break;

      default:
        return res.status(400).json({ error: 'Invalid action' });
    }

    res.json({ success: true, results });
  } catch (error) {
    console.error('Bulk action error:', error);
    res.status(500).json({ error: 'Failed to perform bulk action' });
  }
});

// Bulk operations for users
router.post('/users/bulk-action', requireAdmin, async (req, res) => {
  try {
    const { action, userIds } = req.body;

    if (!action || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ error: 'Action and user IDs are required' });
    }

    let results = [];

    switch (action) {
      case 'activate':
        for (const userId of userIds) {
          try {
            await User.updateActiveStatus(userId, true);
            results.push({ userId, success: true });
          } catch (error) {
            results.push({ userId, success: false, error: error.message });
          }
        }
        break;

      case 'deactivate':
        for (const userId of userIds) {
          try {
            await User.updateActiveStatus(userId, false);
            results.push({ userId, success: true });
          } catch (error) {
            results.push({ userId, success: false, error: error.message });
          }
        }
        break;

      case 'delete':
        const currentUserId = req.session.userId;
        for (const userId of userIds) {
          try {
            // Prevent self-deletion
            if (userId === currentUserId) {
              results.push({ userId, success: false, error: 'Cannot delete your own account' });
              continue;
            }

            // Check if user exists and get role
            const existingUser = await User.findById(userId);
            if (!existingUser) {
              results.push({ userId, success: false, error: 'User not found' });
              continue;
            }

            // Prevent deletion of admin users
            if (existingUser.role === 'admin') {
              results.push({ userId, success: false, error: 'Cannot delete admin users' });
              continue;
            }

            await User.deleteUser(userId);
            results.push({ userId, success: true });
          } catch (error) {
            results.push({ userId, success: false, error: error.message });
          }
        }
        break;

      case 'change_plan':
        const { planId } = req.body;
        if (!planId) {
          return res.status(400).json({ error: 'Plan ID is required for plan change' });
        }

        const plan = await Subscription.getPlanById(planId);
        if (!plan) {
          return res.status(404).json({ error: 'Plan not found' });
        }

        for (const userId of userIds) {
          try {
            await User.updatePlan(userId, plan.name, plan.max_streaming_slots, plan.max_storage_gb);
            results.push({ userId, success: true });
          } catch (error) {
            results.push({ userId, success: false, error: error.message });
          }
        }
        break;

      default:
        return res.status(400).json({ error: 'Invalid action' });
    }

    res.json({ success: true, results });
  } catch (error) {
    console.error('Bulk user action error:', error);
    res.status(500).json({ error: 'Failed to perform bulk action' });
  }
});

// System statistics (with caching)
router.get('/stats', requireAdmin, async (req, res) => {
  try {
    const cacheService = require('../services/cacheService');
    const cacheKey = 'admin_system_stats';

    // Cache disabled - skip cache check
    const stats = await getSystemStats();

    // Cache disabled - skip caching result

    res.json(stats);
  } catch (error) {
    console.error('System stats error:', error);
    res.status(500).json({ error: 'Failed to get system statistics' });
  }
});

// Load balancer management page
router.get('/load-balancer', requireAdmin, async (req, res) => {
  try {
    const loadBalancer = require('../services/loadBalancer');
    const status = loadBalancer.getStatus();
    const metrics = loadBalancer.getMetrics();

    res.render('admin/load-balancer', {
      title: 'Load Balancer Management',
      active: 'admin-load-balancer',
      status,
      metrics
    });
  } catch (error) {
    console.error('Load balancer page error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load load balancer page',
      error: error
    });
  }
});

// Performance monitoring page
router.get('/performance', requireAdmin, async (req, res) => {
  try {
    const performanceMonitor = require('../services/performanceMonitor');
    const cacheService = require('../services/cacheService');
    const dbOptimizer = require('../db/optimizations');

    const performanceReport = performanceMonitor.getDetailedReport();
    const cacheStats = cacheService.getStats();
    const dbStats = await dbOptimizer.getDatabaseStats();

    res.render('admin/performance', {
      title: 'Performance Monitoring',
      active: 'admin-performance',
      performanceReport,
      cacheStats,
      dbStats
    });
  } catch (error) {
    console.error('Performance page error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load performance page',
      error: error
    });
  }
});

// Referral settlement management
router.get('/settlements', requireAdmin, async (req, res) => {
  try {
    const { db } = require('../db/database');

    // Get pending withdrawal requests
    const withdrawalRequests = await new Promise((resolve, reject) => {
      db.all(
        `SELECT wr.*, u.username, u.email
         FROM withdrawal_requests wr
         JOIN users u ON wr.user_id = u.id
         ORDER BY wr.requested_at DESC`,
        [],
        (err, rows) => {
          if (err) reject(err);
          else {
            // Debug logging to check data structure
            if (rows && rows.length > 0) {
              console.log('Withdrawal requests data sample:', JSON.stringify(rows[0], null, 2));
            }
            resolve(rows || []);
          }
        }
      );
    });

    // Get referral statistics
    const referralStats = await new Promise((resolve, reject) => {
      // Get basic referral stats
      db.get(
        `SELECT
           COUNT(*) as total_referrals,
           COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_referrals,
           COALESCE(SUM(CASE WHEN status = 'completed' THEN commission_amount ELSE 0 END), 0) as total_commissions_from_referrals
         FROM referrals`,
        [],
        (err, referralData) => {
          if (err) {
            reject(err);
            return;
          }

          // Get total earnings from referral_earnings table
          db.get(
            `SELECT COALESCE(SUM(amount), 0) as total_earnings_paid
             FROM referral_earnings
             WHERE status = 'completed'`,
            [],
            (err, earningsData) => {
              if (err) {
                reject(err);
                return;
              }

              // Combine the results
              const stats = {
                total_referrals: referralData.total_referrals || 0,
                completed_referrals: referralData.completed_referrals || 0,
                total_commissions: Math.max(
                  referralData.total_commissions_from_referrals || 0,
                  earningsData.total_earnings_paid || 0
                ),
                total_earnings_paid: earningsData.total_earnings_paid || 0
              };

              resolve(stats);
            }
          );
        }
      );
    });

    res.render('admin/settlements', {
      title: res.locals.t('admin_referral.title'),
      active: 'admin-settlements',
      withdrawalRequests,
      referralStats,
      csrfToken: req.csrfToken ? req.csrfToken() : ''
    });
  } catch (error) {
    console.error('Settlements page error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load settlements page',
      error: error
    });
  }
});

// Process withdrawal request (approve/reject)
router.post('/settlements/:id/process', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { action, notes } = req.body;
    const adminId = req.session.userId;

    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid action'
      });
    }

    const { db } = require('../db/database');

    // Get withdrawal request
    const withdrawal = await new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM withdrawal_requests WHERE id = ?`,
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!withdrawal) {
      return res.status(404).json({
        success: false,
        error: 'Withdrawal request not found'
      });
    }

    if (withdrawal.status !== 'pending') {
      return res.status(400).json({
        success: false,
        error: 'Withdrawal request already processed'
      });
    }

    if (action === 'approve') {
      // Update withdrawal status to approved
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE withdrawal_requests
           SET status = 'approved', admin_notes = ?, processed_at = CURRENT_TIMESTAMP, processed_by = ?
           WHERE id = ?`,
          [notes || 'Approved by admin', adminId, id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Send notification to user about approved withdrawal
      const notificationService = require('../services/notificationService');
      try {
        await notificationService.notifyUserSpecific(
          withdrawal.user_id,
          'Penarikan Saldo Disetujui',
          `Permintaan penarikan saldo sebesar Rp ${withdrawal.amount.toLocaleString('id-ID')} telah disetujui. Dana akan segera ditransfer ke rekening Anda.`,
          'success',
          'high',
          {
            withdrawalId: id,
            amount: withdrawal.amount,
            action: 'withdrawal_approved',
            adminNotes: notes || 'Approved by admin'
          }
        );
      } catch (notifError) {
        console.error('Error sending withdrawal approval notification:', notifError);
      }

      res.json({
        success: true,
        message: 'Withdrawal request approved successfully'
      });
    } else {
      // Reject withdrawal - return money to user balance
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users
           SET referral_balance = referral_balance + ?
           WHERE id = ?`,
          [withdrawal.amount, withdrawal.user_id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Update withdrawal status to rejected
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE withdrawal_requests
           SET status = 'rejected', admin_notes = ?, processed_at = CURRENT_TIMESTAMP, processed_by = ?
           WHERE id = ?`,
          [notes || 'Rejected by admin', adminId, id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Send notification to user about rejected withdrawal
      const notificationService = require('../services/notificationService');
      try {
        await notificationService.notifyUserSpecific(
          withdrawal.user_id,
          'Penarikan Saldo Ditolak',
          `Permintaan penarikan saldo sebesar Rp ${withdrawal.amount.toLocaleString('id-ID')} ditolak. Saldo telah dikembalikan ke akun Anda. ${notes ? 'Alasan: ' + notes : ''}`,
          'warning',
          'high',
          {
            withdrawalId: id,
            amount: withdrawal.amount,
            action: 'withdrawal_rejected',
            adminNotes: notes || 'Rejected by admin'
          }
        );
      } catch (notifError) {
        console.error('Error sending withdrawal rejection notification:', notifError);
      }

      res.json({
        success: true,
        message: 'Withdrawal request rejected and balance restored'
      });
    }
  } catch (error) {
    console.error('Process withdrawal error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process withdrawal request'
    });
  }
});

// Modal generator page for promotional content
router.get('/modal-generator', requireAdmin, async (req, res) => {
  try {
    res.render('admin/modal-generator', {
      title: 'Modal Generator',
      active: 'admin-modal-generator'
    });
  } catch (error) {
    console.error('Modal generator page error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load modal generator page',
      error: error
    });
  }
});

// Stream monitoring page
router.get('/stream-monitor', requireAdmin, async (req, res) => {
  try {
    const streamingService = require('../services/streamingService');
    const Stream = require('../models/Stream');
    const User = require('../models/User');

    // Get active streams from memory
    const activeStreamIds = streamingService.getActiveStreams();

    // Get all streams with their details (pass null to get all streams)
    const allStreams = await Stream.findAll(null);

    // Get active streams with user details
    const activeStreamsWithDetails = [];
    for (const streamId of activeStreamIds) {
      const stream = allStreams.find(s => s.id === streamId);
      if (stream) {
        const user = await User.findById(stream.user_id);
        const logs = streamingService.getStreamLogs(streamId);
        activeStreamsWithDetails.push({
          ...stream,
          user: user || { username: 'Unknown', email: 'N/A' },
          logs: Array.isArray(logs) ? logs.slice(-5) : [] // Only last 5 logs, ensure it's an array
        });
      }
    }

    // Get all streams for management
    const streamsWithUsers = [];
    for (const stream of allStreams) {
      const user = await User.findById(stream.user_id);
      streamsWithUsers.push({
        ...stream,
        user: user || { username: 'Unknown', email: 'N/A' }
      });
    }

    res.render('admin/stream-monitor', {
      title: 'Stream Monitor',
      active: 'admin-stream-monitor',
      activeStreams: activeStreamsWithDetails,
      allStreams: streamsWithUsers,
      totalActiveStreams: activeStreamIds.length
    });
  } catch (error) {
    console.error('Stream monitor page error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load stream monitor page',
      error: error
    });
  }
});

// Stream control API endpoints
router.post('/api/stream/:id/start', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const streamingService = require('../services/streamingService');

    const result = await streamingService.startStream(id);

    if (result.success) {
      res.json({
        success: true,
        message: 'Stream started successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error || 'Failed to start stream'
      });
    }
  } catch (error) {
    console.error('Error starting stream:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

router.post('/api/stream/:id/stop', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const streamingService = require('../services/streamingService');

    const result = await streamingService.stopStream(id);

    if (result.success) {
      res.json({
        success: true,
        message: 'Stream stopped successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error || 'Failed to stop stream'
      });
    }
  } catch (error) {
    console.error('Error stopping stream:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Get real-time stream status
router.get('/api/stream-status', requireAdmin, async (req, res) => {
  try {
    const streamingService = require('../services/streamingService');
    const Stream = require('../models/Stream');
    const User = require('../models/User');

    // Get active streams from memory
    const activeStreamIds = streamingService.getActiveStreams();

    // Get all streams with their details (pass null to get all streams)
    const allStreams = await Stream.findAll(null);

    // Get active streams with user details
    const activeStreamsWithDetails = [];
    for (const streamId of activeStreamIds) {
      const stream = allStreams.find(s => s.id === streamId);
      if (stream) {
        const user = await User.findById(stream.user_id);
        activeStreamsWithDetails.push({
          id: stream.id,
          title: stream.title,
          status: stream.status,
          platform: stream.platform,
          user: {
            id: user?.id || 'unknown',
            username: user?.username || 'Unknown',
            email: user?.email || 'N/A'
          },
          startTime: Date.now(), // This would need to be tracked properly
          logs: (() => {
            const logs = streamingService.getStreamLogs(streamId);
            return Array.isArray(logs) ? logs.slice(-5) : []; // Ensure it's an array
          })()
        });
      }
    }

    res.json({
      success: true,
      activeStreams: activeStreamsWithDetails,
      totalActive: activeStreamIds.length
    });
  } catch (error) {
    console.error('Error fetching stream status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch stream status'
    });
  }
});

// Get user details with quota info
router.get('/users/:userId', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      success: true,
      user
    });
  } catch (error) {
    console.error('Get user details error:', error);
    res.status(500).json({ error: 'Failed to get user details' });
  }
});

// Edit user
router.post('/users/edit', requireAdmin, async (req, res) => {
  try {
    const {
      userId,
      username,
      email,
      role,
      plan,
      planId,
      customEndDate,
      useProrated,
      max_streaming_slots,
      max_storage_gb,
      is_active,
      new_password
    } = req.body;

    if (!userId || !username) {
      return res.status(400).json({ error: 'User ID and username are required' });
    }

    // Check if user exists
    const existingUser = await User.findById(userId);
    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Handle storage conversion (frontend already converts MB to GB)
    const storageGB = max_storage_gb || 2;

    // Update user data
    const updateData = {
      username,
      email: email || null,
      role: role || 'user',
      plan_type: plan || 'Preview',
      max_streaming_slots: max_streaming_slots || 0,
      max_storage_gb: storageGB,
      is_active: is_active ? 1 : 0
    };

    // Update password if provided
    if (new_password) {
      const bcrypt = require('bcrypt');
      updateData.password = await bcrypt.hash(new_password, 10);
    }

    await User.updateUser(userId, updateData);

    // If plan changed and planId provided, update subscription
    if (planId && existingUser.plan_type !== plan) {
      try {
        // Use the enhanced plan update endpoint
        const planUpdateData = {
          planId,
          customEndDate,
          useProrated
        };

        const response = await fetch(`http://localhost:${process.env.PORT || 7575}/admin/users/${userId}/plan`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(planUpdateData)
        });

        if (!response.ok) {
          console.warn('Failed to update subscription, but user data updated successfully');
        }
      } catch (error) {
        console.warn('Failed to update subscription:', error.message);
        // Continue - user data was still updated successfully
      }
    }

    res.json({ success: true, message: 'User updated successfully' });
  } catch (error) {
    console.error('Edit user error:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Delete user
router.post('/users/:userId/delete', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUserId = req.session.userId;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Prevent self-deletion
    if (userId === currentUserId) {
      return res.status(400).json({ error: 'You cannot delete your own account' });
    }

    // Check if user exists
    const existingUser = await User.findById(userId);
    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Prevent deletion of other admin users (optional safety measure)
    if (existingUser.role === 'admin') {
      return res.status(400).json({ error: 'Cannot delete admin users' });
    }

    // Delete the user and all related data
    await User.deleteUser(userId);

    res.json({ success: true, message: 'User deleted successfully' });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
});

// Helper function to get system statistics
async function getSystemStats() {
  const { db } = require('../db/database');

  return new Promise((resolve, reject) => {
    db.all(`
      SELECT
        (SELECT COUNT(*) FROM users) as total_users,
        (SELECT COUNT(*) FROM users WHERE is_active = 1) as active_users,
        (SELECT COUNT(*) FROM streams) as total_streams,
        (SELECT COUNT(*) FROM streams WHERE status = 'live') as live_streams,
        (SELECT COUNT(*) FROM videos) as total_videos,
        (SELECT SUM(file_size) FROM videos) as total_storage_bytes,
        (SELECT COUNT(*) FROM users WHERE plan_type != 'Preview' AND plan_type IS NOT NULL) as active_subscriptions
    `, [], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        const stats = rows[0];
        stats.total_storage_gb = stats.total_storage_bytes ?
          (stats.total_storage_bytes / (1024 * 1024 * 1024)).toFixed(2) : 0;
        resolve(stats);
      }
    });
  });
}

// Give trial to user
router.post('/users/:userId/give-trial', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { durationDays, trialSlots = 1, trialStorageGB = 0.05 } = req.body;

    if (!durationDays || durationDays < 1 || durationDays > 365) {
      return res.status(400).json({ error: 'Duration must be between 1 and 365 days' });
    }

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if user already has an active trial
    const activeTrial = await User.hasActiveTrial(userId);
    if (activeTrial) {
      return res.status(400).json({
        error: 'User already has an active trial',
        trial_end_date: activeTrial.trial_end_date
      });
    }

    // Give trial to user
    const trialResult = await User.giveTrial(userId, parseInt(durationDays), parseInt(trialSlots), parseFloat(trialStorageGB));

    res.json({
      success: true,
      message: 'Trial given successfully',
      trial: trialResult
    });
  } catch (error) {
    console.error('Give trial error:', error);
    res.status(500).json({ error: 'Failed to give trial to user' });
  }
});

// Remove trial from user
router.post('/users/:userId/remove-trial', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Remove trial
    await User.removeExpiredTrial(userId);

    res.json({
      success: true,
      message: 'Trial removed successfully'
    });
  } catch (error) {
    console.error('Remove trial error:', error);
    res.status(500).json({ error: 'Failed to remove trial from user' });
  }
});

// Subscription Monitor Management
router.get('/subscription-monitor', requireAdmin, async (req, res) => {
  try {
    const stats = subscriptionMonitor.getStats();

    res.render('admin/subscription-monitor', {
      title: 'Subscription Monitor',
      active: 'admin-subscription-monitor',
      stats
    });
  } catch (error) {
    console.error('Subscription monitor page error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load subscription monitor',
      error: error
    });
  }
});

// Get subscription monitor stats (API)
router.get('/api/subscription-monitor/stats', requireAdmin, async (req, res) => {
  try {
    const stats = subscriptionMonitor.getStats();
    res.json({ success: true, stats });
  } catch (error) {
    console.error('Get subscription monitor stats error:', error);
    res.status(500).json({ error: 'Failed to get subscription monitor stats' });
  }
});

// Force run subscription monitor check
router.post('/api/subscription-monitor/force-check', requireAdmin, async (req, res) => {
  try {
    const result = await subscriptionMonitor.forceCheck();
    res.json({
      success: true,
      message: 'Subscription monitor check completed',
      stats: result
    });
  } catch (error) {
    console.error('Force subscription monitor check error:', error);
    res.status(500).json({ error: 'Failed to run subscription monitor check' });
  }
});

// Start/Stop subscription monitor
router.post('/api/subscription-monitor/toggle', requireAdmin, async (req, res) => {
  try {
    const { action } = req.body;

    if (action === 'start') {
      subscriptionMonitor.start();
      res.json({ success: true, message: 'Subscription monitor started' });
    } else if (action === 'stop') {
      subscriptionMonitor.stop();
      res.json({ success: true, message: 'Subscription monitor stopped' });
    } else {
      res.status(400).json({ error: 'Invalid action. Use "start" or "stop"' });
    }
  } catch (error) {
    console.error('Toggle subscription monitor error:', error);
    res.status(500).json({ error: 'Failed to toggle subscription monitor' });
  }
});

// Update subscription monitor interval
router.post('/api/subscription-monitor/interval', requireAdmin, async (req, res) => {
  try {
    const { minutes } = req.body;

    if (!minutes || minutes < 1) {
      return res.status(400).json({ error: 'Interval must be at least 1 minute' });
    }

    subscriptionMonitor.setCheckInterval(minutes);
    res.json({
      success: true,
      message: `Subscription monitor interval updated to ${minutes} minutes`
    });
  } catch (error) {
    console.error('Update subscription monitor interval error:', error);
    res.status(500).json({ error: 'Failed to update subscription monitor interval' });
  }
});

// Emergency stop user streams
router.post('/api/subscription-monitor/emergency-stop/:userId', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { reason } = req.body;

    const result = await subscriptionMonitor.emergencyStopUserStreams(
      userId,
      reason || 'Emergency stop by admin'
    );

    res.json({
      success: true,
      message: `Emergency stopped ${result.stopped} streams for user ${userId}`,
      result
    });
  } catch (error) {
    console.error('Emergency stop user streams error:', error);
    res.status(500).json({ error: 'Failed to emergency stop user streams' });
  }
});

// Check specific user subscription
router.get('/api/subscription-monitor/user/:userId', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const result = await subscriptionMonitor.checkUserSubscription(userId);

    res.json({ success: true, result });
  } catch (error) {
    console.error('Check user subscription error:', error);
    res.status(500).json({ error: 'Failed to check user subscription' });
  }
});

// Manual storage sync endpoint (admin only)
router.post('/sync-storage', requireAdmin, async (req, res) => {
  try {
    const syncedUsers = await QuotaMiddleware.syncAllUsersStorageCache();
    
    res.json({
      success: true,
      message: `Storage cache synced for ${syncedUsers} users`,
      syncedUsers
    });
    
  } catch (error) {
    console.error('Error in manual storage sync:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to sync storage cache'
    });
  }
});

module.exports = router;
