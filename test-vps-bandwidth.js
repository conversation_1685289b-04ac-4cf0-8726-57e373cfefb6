const http = require('http');
const { Writable } = require('stream');

const TEST_URL = 'http://speedtest.tele2.net/10MB.zip'; // A 10MB file for testing
const TEST_DURATION = 10000; // 10 seconds

async function testDownloadSpeed() {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    let downloadedBytes = 0;

    const request = http.get(TEST_URL, (response) => {
      response.on('data', (chunk) => {
        downloadedBytes += chunk.length;
      });

      response.on('end', () => {
        const duration = (Date.now() - startTime) / 1000;
        const speedBps = (downloadedBytes * 8) / duration;
        const speedMbps = (speedBps / 1000 / 1000).toFixed(2);
        console.log(`Download test completed in ${duration.toFixed(2)}s`);
        resolve(speedMbps);
      });
    });

    request.on('error', (err) => {
      reject(new Error(`Download test failed: ${err.message}`));
    });
  });
}

async function testUploadSpeed() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'google.com', // A reliable host to test against
      port: 80,
      path: '/',
      method: 'POST',
      headers: {
        'Content-Type': 'application/octet-stream',
      },
    };

    const startTime = Date.now();
    let uploadedBytes = 0;

    const request = http.request(options, (response) => {
      // We don't care about the response, just that the upload finished
      response.on('data', () => {});
      response.on('end', () => {
        const duration = (Date.now() - startTime) / 1000;
        const speedBps = (uploadedBytes * 8) / duration;
        const speedMbps = (speedBps / 1000 / 1000).toFixed(2);
        console.log(`Upload test completed in ${duration.toFixed(2)}s`);
        resolve(speedMbps);
      });
    });

    request.on('error', (err) => {
      reject(new Error(`Upload test failed: ${err.message}`));
    });

    // Write data for 10 seconds
    const interval = setInterval(() => {
      const chunk = Buffer.alloc(1024 * 1024, 'a'); // 1MB chunk
      request.write(chunk);
      uploadedBytes += chunk.length;
    }, 100);

    setTimeout(() => {
      clearInterval(interval);
      request.end();
    }, TEST_DURATION);
  });
}

async function runBandwidthTest() {
  try {
    console.log('Starting bandwidth test...');
    
    console.log('\n--- Testing Download Speed ---');
    const downloadSpeed = await testDownloadSpeed();
    console.log(`Estimated Download Speed: ${downloadSpeed} Mbps`);

    console.log('\n--- Testing Upload Speed ---');
    const uploadSpeed = await testUploadSpeed();
    console.log(`Estimated Upload Speed: ${uploadSpeed} Mbps`);

    console.log('\n--- Summary ---');
    console.log(`If these speeds are significantly lower than what your VPS provider advertises, it could be the cause of the upload issues.`);
    console.log('Consider contacting your VPS provider or upgrading your plan if the speeds are insufficient.');

  } catch (error) {
    console.error('\n--- Test Failed ---');
    console.error(error.message);
  }
}

runBandwidthTest();
