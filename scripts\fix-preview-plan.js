/**
 * Fix Preview Plan Configuration
 * This script ensures the Preview plan is properly configured with 0 streaming slots
 */

const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

async function checkAndFixPreviewPlan() {
  console.log('🔍 Checking Preview plan configuration...\n');

  try {
    // Check if Preview plan exists
    const previewPlan = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM subscription_plans WHERE LOWER(TRIM(name)) = LOWER(TRIM(?)) AND is_active = 1',
        ['Preview'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!previewPlan) {
      console.log('❌ Preview plan not found! Creating it...');
      
      // Create Preview plan with correct settings
      const previewPlanId = uuidv4();
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO subscription_plans 
           (id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features, is_active)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            previewPlanId,
            'Preview',
            0,
            'USD',
            'unlimited',
            0, // 0 streaming slots for Preview plan
            0.015, // 15MB storage (0.015 GB)
            JSON.stringify(['Basic features', 'Limited storage', 'No streaming']),
            1
          ],
          function (err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
      
      console.log('✅ Preview plan created successfully!');
    } else {
      console.log('✅ Preview plan found:', {
        id: previewPlan.id,
        name: previewPlan.name,
        max_streaming_slots: previewPlan.max_streaming_slots,
        max_storage_gb: previewPlan.max_storage_gb,
        price: previewPlan.price
      });

      // Check if Preview plan has correct settings
      if (previewPlan.max_streaming_slots !== 0) {
        console.log('⚠️ Preview plan has incorrect streaming slots! Fixing...');
        
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE subscription_plans SET max_streaming_slots = 0 WHERE id = ?',
            [previewPlan.id],
            function (err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });
        
        console.log('✅ Preview plan streaming slots fixed to 0');
      }

      // Check storage limit (should be 15MB = 0.015GB)
      if (previewPlan.max_storage_gb !== 0.015) {
        console.log('⚠️ Preview plan has incorrect storage limit! Fixing...');
        
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE subscription_plans SET max_storage_gb = 0.015 WHERE id = ?',
            [previewPlan.id],
            function (err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });
        
        console.log('✅ Preview plan storage limit fixed to 15MB (0.015GB)');
      }
    }

    // Now check users who should be on Preview plan but have wrong limits
    console.log('\n🔍 Checking users with expired subscriptions...');
    
    const usersWithExpiredSubs = await new Promise((resolve, reject) => {
      db.all(`
        SELECT u.id, u.username, u.plan_type, u.max_streaming_slots, u.max_storage_gb,
               us.end_date, us.status, sp.name as subscription_plan_name
        FROM users u
        LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
        LEFT JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE (us.end_date < datetime('now') OR us.end_date IS NULL OR sp.name = 'Preview')
        AND u.plan_type = 'Preview'
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`Found ${usersWithExpiredSubs.length} users on Preview plan`);

    // Fix users who have wrong limits
    let fixedUsers = 0;
    for (const user of usersWithExpiredSubs) {
      if (user.max_streaming_slots !== 0 || user.max_storage_gb !== 0.015) {
        console.log(`Fixing user ${user.username} (${user.id}): slots ${user.max_streaming_slots} -> 0, storage ${user.max_storage_gb}GB -> 0.015GB`);
        
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE users SET max_streaming_slots = 0, max_storage_gb = 0.015 WHERE id = ?',
            [user.id],
            function (err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });
        
        fixedUsers++;
      }
    }

    if (fixedUsers > 0) {
      console.log(`✅ Fixed ${fixedUsers} users with incorrect Preview plan limits`);
    } else {
      console.log('✅ All Preview plan users have correct limits');
    }

    // Create other default plans if they don't exist
    await createDefaultPlansIfMissing();

    console.log('\n🎉 Preview plan configuration check completed!');

  } catch (error) {
    console.error('❌ Error checking Preview plan:', error);
  }
}

async function createDefaultPlansIfMissing() {
  console.log('\n🔍 Checking other default plans...');

  const defaultPlans = [
    {
      name: 'PodLite',
      price: 24900,
      currency: 'IDR',
      billing_period: 'monthly',
      max_streaming_slots: 1,
      max_storage_gb: 1,
      features: ['1 Streaming Slot', '1GB Storage', 'Basic Support']
    },
    {
      name: 'PodFlow',
      price: 49900,
      currency: 'IDR',
      billing_period: 'monthly',
      max_streaming_slots: 2,
      max_storage_gb: 2,
      features: ['2 Streaming Slots', '2GB Storage', 'Advanced Settings', 'Google Drive Import']
    },
    {
      name: 'PodPrime',
      price: 99900,
      currency: 'IDR',
      billing_period: 'monthly',
      max_streaming_slots: 5,
      max_storage_gb: 5,
      features: ['5 Streaming Slots', '5GB Storage', 'Premium Support', 'Advanced Settings', 'Google Drive Import']
    }
  ];

  for (const planData of defaultPlans) {
    const existingPlan = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM subscription_plans WHERE LOWER(TRIM(name)) = LOWER(TRIM(?))',
        [planData.name],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!existingPlan) {
      console.log(`Creating ${planData.name} plan...`);
      
      const planId = uuidv4();
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO subscription_plans 
           (id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features, is_active)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            planId,
            planData.name,
            planData.price,
            planData.currency,
            planData.billing_period,
            planData.max_streaming_slots,
            planData.max_storage_gb,
            JSON.stringify(planData.features),
            1
          ],
          function (err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
      
      console.log(`✅ ${planData.name} plan created`);
    } else {
      console.log(`✅ ${planData.name} plan already exists`);
    }
  }
}

// Run the fix if this file is executed directly
if (require.main === module) {
  checkAndFixPreviewPlan().then(() => {
    console.log('\n✅ All done! You can now test the subscription system.');
    process.exit(0);
  }).catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = { checkAndFixPreviewPlan };
