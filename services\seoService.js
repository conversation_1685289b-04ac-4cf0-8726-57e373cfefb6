const path = require('path');

class SEOService {
  constructor() {
    this.baseUrl = process.env.BASE_URL || 'https://streamonpod.com';
    this.siteName = 'StreamOnPod';
    this.description = 'Platform streaming berbasis cloud untuk siaran konten berkelanjutan di berbagai platform. Ubah video Anda menjadi live stream otomatis dengan infrastruktur tingkat enterprise.';
    this.descriptionEn = 'Cloud-powered streaming platform for continuous content broadcasting across multiple platforms. Transform your videos into automated live streams with enterprise-grade infrastructure.';
  }

  // Generate Organization JSON-LD
  generateOrganizationSchema(locale = 'id') {
    const description = locale === 'en' ? this.descriptionEn : this.description;

    return {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "StreamOnPod",
      "description": description,
      "url": this.baseUrl,
      "logo": `${this.baseUrl}/images/streamonpod-logo.png`,
      "image": `${this.baseUrl}/images/streamonpod-logotype.png`,
      "foundingDate": "2024",
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer support",
        "url": "https://t.me/streamonpod",
        "availableLanguage": ["Indonesian", "English"]
      },
      "sameAs": [
        "https://t.me/streamonpod"
      ],
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "ID",
        "addressRegion": "Indonesia"
      },
      "areaServed": {
        "@type": "Country",
        "name": "Indonesia"
      }
    };
  }

  // Generate SoftwareApplication JSON-LD
  generateSoftwareApplicationSchema(locale = 'id') {
    const description = locale === 'en' ? this.descriptionEn : this.description;
    const offerDescription = locale === 'en' ? 'Free Preview Plan Available' : 'Paket Preview Gratis Tersedia';

    return {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "StreamOnPod",
      "description": description,
      "url": this.baseUrl,
      "applicationCategory": "MultimediaApplication",
      "operatingSystem": "Web Browser",
      "inLanguage": ["id", "en"],
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "IDR",
        "description": offerDescription,
        "availability": "https://schema.org/InStock",
        "eligibleRegion": {
          "@type": "Country",
          "name": "Indonesia"
        }
      },
      "screenshot": `${this.baseUrl}/images/streamonpod-logotype.png`,
      "softwareVersion": "2.0.0",
      "author": {
        "@type": "Organization",
        "name": "StreamOnPod"
      },
      "publisher": {
        "@type": "Organization",
        "name": "StreamOnPod"
      }
    };
  }

  // Generate WebSite JSON-LD with search functionality
  generateWebSiteSchema(locale = 'id') {
    const description = locale === 'en' ? this.descriptionEn : this.description;

    return {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "StreamOnPod",
      "description": description,
      "url": this.baseUrl,
      "inLanguage": ["id", "en"],
      "potentialAction": {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": `${this.baseUrl}/dashboard?search={search_term_string}`
        },
        "query-input": "required name=search_term_string"
      },
      "publisher": {
        "@type": "Organization",
        "name": "StreamOnPod"
      },
      "audience": {
        "@type": "Audience",
        "geographicArea": {
          "@type": "Country",
          "name": "Indonesia"
        }
      }
    };
  }

  // Generate VideoObject JSON-LD for video content
  generateVideoObjectSchema(video) {
    if (!video) return null;

    return {
      "@context": "https://schema.org",
      "@type": "VideoObject",
      "name": video.title || "StreamOnPod Video",
      "description": video.description || "Video content on StreamOnPod streaming platform",
      "thumbnailUrl": video.thumbnail_path ? `${this.baseUrl}${video.thumbnail_path}` : `${this.baseUrl}/images/default-thumbnail.jpg`,
      "uploadDate": video.created_at || new Date().toISOString(),
      "duration": video.duration ? `PT${Math.floor(video.duration)}S` : undefined,
      "contentUrl": video.file_path ? `${this.baseUrl}${video.file_path}` : undefined,
      "embedUrl": `${this.baseUrl}/video/${video.id}`,
      "publisher": {
        "@type": "Organization",
        "name": "StreamOnPod",
        "logo": {
          "@type": "ImageObject",
          "url": `${this.baseUrl}/images/streamonpod-logo.png`
        }
      },
      "author": {
        "@type": "Person",
        "name": video.username || "StreamOnPod User"
      }
    };
  }

  // Generate Offer JSON-LD for subscription plans
  generateOfferSchema(plan) {
    if (!plan) return null;

    return {
      "@context": "https://schema.org",
      "@type": "Offer",
      "name": plan.name,
      "description": plan.description || `${plan.name} streaming plan`,
      "price": plan.price || "0",
      "priceCurrency": "IDR",
      "availability": "https://schema.org/InStock",
      "validFrom": new Date().toISOString(),
      "seller": {
        "@type": "Organization",
        "name": "StreamOnPod"
      },
      "category": "Streaming Service",
      "eligibleRegion": {
        "@type": "Country",
        "name": "Indonesia"
      }
    };
  }

  // Generate BreadcrumbList JSON-LD
  generateBreadcrumbSchema(breadcrumbs) {
    if (!breadcrumbs || !Array.isArray(breadcrumbs)) return null;

    const itemListElement = breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url ? `${this.baseUrl}${crumb.url}` : undefined
    }));

    return {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": itemListElement
    };
  }

  // Generate Open Graph meta tags
  generateOpenGraphTags(options = {}) {
    const {
      title = 'StreamOnPod - Platform Streaming Cloud',
      description = this.description,
      image = `${this.baseUrl}/images/streamonpod-logotype.png`,
      url = this.baseUrl,
      type = 'website',
      locale = 'id'
    } = options;

    const ogLocale = locale === 'en' ? 'en_US' : 'id_ID';
    const finalDescription = locale === 'en' ? this.descriptionEn : description;

    // Ensure image URL is absolute for social media platforms
    const absoluteImageUrl = image.startsWith('http') ? image : `${this.baseUrl}${image.startsWith('/') ? '' : '/'}${image}`;

    return {
      'og:title': title,
      'og:description': finalDescription,
      'og:image': absoluteImageUrl,
      'og:image:width': '1200',
      'og:image:height': '630',
      'og:image:alt': 'StreamOnPod - Platform Streaming Cloud',
      'og:url': url,
      'og:type': type,
      'og:site_name': 'StreamOnPod',
      'og:locale': ogLocale,
      'og:locale:alternate': locale === 'en' ? 'id_ID' : 'en_US'
    };
  }

  // Generate Twitter Card meta tags
  generateTwitterCardTags(options = {}) {
    const {
      title = 'StreamOnPod - Platform Streaming Cloud',
      description = this.description,
      image = `${this.baseUrl}/images/streamonpod-logotype.png`,
      locale = 'id'
    } = options;

    const finalDescription = locale === 'en' ? this.descriptionEn : description;

    // Ensure image URL is absolute for social media platforms
    const absoluteImageUrl = image.startsWith('http') ? image : `${this.baseUrl}${image.startsWith('/') ? '' : '/'}${image}`;

    return {
      'twitter:card': 'summary_large_image',
      'twitter:title': title,
      'twitter:description': finalDescription,
      'twitter:image': absoluteImageUrl,
      'twitter:image:alt': 'StreamOnPod - Platform Streaming Cloud',
      'twitter:site': '@streamonpod',
      'twitter:creator': '@streamonpod'
    };
  }

  // Generate canonical URL
  generateCanonicalUrl(path = '') {
    return `${this.baseUrl}${path}`;
  }

  // Generate complete SEO data for a page
  generatePageSEO(options = {}) {
    const {
      title,
      description,
      image,
      path = '',
      video = null,
      plan = null,
      breadcrumbs = null,
      includeOrganization = true,
      includeSoftwareApp = false,
      includeWebSite = false,
      locale = 'id'
    } = options;

    const schemas = [];

    // Add organization schema
    if (includeOrganization) {
      schemas.push(this.generateOrganizationSchema(locale));
    }

    // Add software application schema
    if (includeSoftwareApp) {
      schemas.push(this.generateSoftwareApplicationSchema(locale));
    }

    // Add website schema
    if (includeWebSite) {
      schemas.push(this.generateWebSiteSchema(locale));
    }

    // Add video schema if provided
    if (video) {
      const videoSchema = this.generateVideoObjectSchema(video);
      if (videoSchema) schemas.push(videoSchema);
    }

    // Add offer schema if provided
    if (plan) {
      const offerSchema = this.generateOfferSchema(plan);
      if (offerSchema) schemas.push(offerSchema);
    }

    // Add breadcrumb schema if provided
    if (breadcrumbs) {
      const breadcrumbSchema = this.generateBreadcrumbSchema(breadcrumbs);
      if (breadcrumbSchema) schemas.push(breadcrumbSchema);
    }

    return {
      jsonLd: schemas,
      openGraph: this.generateOpenGraphTags({ title, description, image, url: this.generateCanonicalUrl(path), locale }),
      twitterCard: this.generateTwitterCardTags({ title, description, image, locale }),
      canonical: this.generateCanonicalUrl(path)
    };
  }
}

module.exports = new SEOService();
