#!/usr/bin/env node

const { db } = require('./db/database');
const Subscription = require('./models/Subscription');

async function verifyCancellationFix() {
  console.log('🔍 Verifying admin cancellation fix...\n');
  
  try {
    // Check for cancelled subscriptions
    const cancelledSubs = await new Promise((resolve, reject) => {
      db.all(`
        SELECT us.*, sp.name as plan_name, u.username
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        JOIN users u ON us.user_id = u.id
        WHERE us.status = 'cancelled'
        ORDER BY us.updated_at DESC
        LIMIT 3
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    if (cancelledSubs.length === 0) {
      console.log('❌ No cancelled subscriptions found to test with');
      return;
    }
    
    console.log(`📋 Found ${cancelledSubs.length} cancelled subscription(s):\n`);
    
    for (const sub of cancelledSubs) {
      console.log(`👤 User: ${sub.username} (${sub.user_id})`);
      console.log(`   Plan: ${sub.plan_name}`);
      console.log(`   Status: ${sub.status}`);
      console.log(`   End Date: ${sub.end_date}`);
      
      // Test getUserSubscriptionIncludingExpired
      const subWithExpired = await Subscription.getUserSubscriptionIncludingExpired(sub.user_id);
      
      if (subWithExpired) {
        console.log(`   ✅ getUserSubscriptionIncludingExpired: Found ${subWithExpired.plan_name}`);
        console.log(`      - Is Expired: ${subWithExpired.isExpired}`);
        console.log(`      - Is Cancelled: ${subWithExpired.isCancelled}`);
        
        // Check if end_date is in the past (should be for cancelled subscriptions)
        const endDate = new Date(subWithExpired.end_date);
        const now = new Date();
        const isEndDatePast = endDate < now;
        console.log(`      - End date in past: ${isEndDatePast}`);
        
        if (subWithExpired.isExpired && subWithExpired.isCancelled && isEndDatePast) {
          console.log(`   ✅ Cancellation handling is working correctly`);
        } else {
          console.log(`   ❌ Cancellation handling has issues`);
        }
      } else {
        console.log(`   ❌ getUserSubscriptionIncludingExpired: Not found`);
      }
      
      // Test getUserSubscription (should return null)
      const activeSub = await Subscription.getUserSubscription(sub.user_id);
      console.log(`   ${activeSub ? '❌' : '✅'} getUserSubscription: ${activeSub ? 'Found active subscription (unexpected)' : 'No active subscription (expected)'}`);
      
      console.log(''); // Empty line for readability
    }
    
    console.log('🎯 Summary:');
    console.log('   - Cancelled subscriptions should show as expired and cancelled in UI');
    console.log('   - Users should be on Preview plan with 0 slots');
    console.log('   - getUserSubscription should return null for cancelled subscriptions');
    console.log('   - getUserSubscriptionIncludingExpired should show the cancelled subscription for display');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

verifyCancellationFix().then(() => {
  console.log('\n🏁 Verification completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Verification failed:', error);
  process.exit(1);
});
