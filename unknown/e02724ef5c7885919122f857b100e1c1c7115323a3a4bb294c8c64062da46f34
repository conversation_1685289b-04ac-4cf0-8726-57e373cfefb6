const Transaction = require('./models/Transaction');

console.log('Checking Transaction methods...');
console.log('updateMidtransData method exists:', typeof Transaction.updateMidtransData === 'function');
console.log('updateStatus method exists:', typeof Transaction.updateStatus === 'function');
console.log('create method exists:', typeof Transaction.create === 'function');

// List all methods
console.log('\nAll Transaction methods:');
console.log(Object.getOwnPropertyNames(Transaction).filter(name => typeof Transaction[name] === 'function'));

process.exit(0);
