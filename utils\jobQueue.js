const EventEmitter = require('events');

class JobQueue extends EventEmitter {
  constructor(concurrency = 1) {
    super();
    this.concurrency = concurrency;
    this.running = 0;
    this.queue = [];
  }

  add(task) {
    return new Promise((resolve, reject) => {
      this.queue.push({ task, resolve, reject });
      this.emit('add');
      this.next();
    });
  }

  next() {
    if (this.running >= this.concurrency || !this.queue.length) {
      return;
    }

    const { task, resolve, reject } = this.queue.shift();
    this.running++;
    this.emit('start', task);

    task()
      .then(resolve)
      .catch(reject)
      .finally(() => {
        this.running--;
        this.emit('end', task);
        this.next();
      });
  }

  get size() {
    return this.queue.length;
  }

  get isIdle() {
    return this.running === 0 && this.queue.length === 0;
  }
}

module.exports = new JobQueue();