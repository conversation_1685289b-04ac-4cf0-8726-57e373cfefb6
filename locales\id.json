{"common": {"streamonpod": "StreamOnPod", "dashboard": "Dashboard", "streams": "Streaming", "gallery": "<PERSON><PERSON>", "history": "Riwayat", "plans": "<PERSON><PERSON>", "admin": "Admin", "settings": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON>", "support": "Dukungan", "help": "Bantuan & Dukungan", "profile": "Profil", "notifications": "Notif<PERSON><PERSON>", "loading": "Memuat...", "save": "Simpan", "cancel": "<PERSON><PERSON>", "delete": "Hapus", "edit": "Edit", "create": "Buat", "update": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "back": "Kembali", "next": "Selanjutnya", "previous": "Sebelumnya", "search": "<PERSON><PERSON>", "filter": "Filter", "sort": "Urut<PERSON>", "refresh": "Refresh", "export": "Ekspor", "import": "Impor", "download": "<PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yes": "Ya", "no": "Tidak", "ok": "OK", "error": "Error", "success": "<PERSON><PERSON><PERSON><PERSON>", "warning": "Peringatan", "info": "Informasi", "language": "Bahasa", "english": "English", "indonesian": "Bahasa Indonesia"}, "auth": {"login_title": "Masuk ke StreamOnPod", "login_subtitle": "Selamat datang kembali! Silakan masuk ke akun Anda", "register_title": "Buat A<PERSON>n", "register_subtitle": "Bergabung dengan StreamOnPod dan mulai streaming", "email": "<PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON>", "confirm_password": "<PERSON>n<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON>", "full_name": "<PERSON><PERSON>", "remember_me": "<PERSON>gat saya", "forgot_password": "Lupa kata sandi?", "dont_have_account": "Belum punya akun?", "already_have_account": "Sudah punya akun?", "sign_up": "<PERSON><PERSON><PERSON>", "sign_in": "<PERSON><PERSON><PERSON>", "create_account": "Buat A<PERSON>n", "login_button": "<PERSON><PERSON><PERSON>", "register_button": "Buat A<PERSON>n", "logout_success": "<PERSON>a telah ber<PERSON><PERSON> keluar", "login_required": "<PERSON><PERSON>an masuk untuk mengakses halaman ini", "invalid_credentials": "Email atau kata sandi tidak valid", "registration_success": "<PERSON><PERSON><PERSON> ber<PERSON> di<PERSON>at", "email_exists": "<PERSON>ail sudah terdaftar", "username_exists": "<PERSON>a pengguna sudah digunakan"}, "dashboard": {"title": "Kelola Streaming", "subtitle": "Kelola live streaming dan konten <PERSON>a", "welcome": "Selamat datang kembali", "create_stream": "Buat Streaming Baru", "create_stream_description": "Buat streaming pertama Anda untuk mulai menyiarkan ke platform favorit", "buy_plan": "<PERSON><PERSON>", "upgrade_plan": "Upgrade Paket", "my_streams": "Streaming Saya", "active_streams": "Streaming Aktif", "total_streams": "Total Streaming", "storage_used": "Penyimpanan Te<PERSON>akai", "streaming_quota": "Kuota Streaming", "streaming_quota_desc": "Slot streaming yang dapat digunakan secara bersamaan", "storage_used_desc": "Storage digunakan untuk menyimpan video yang diupload ke gallery", "no_streams": "Tidak ada streaming ditemukan", "stream_status": {"active": "Aktif", "inactive": "Tidak Aktif", "error": "Error", "starting": "<PERSON><PERSON><PERSON>", "stopping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "stream_actions": {"start": "<PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Edit", "delete": "Hapus", "view": "Lihat"}, "upgrade_to_stream": "Upgrade untuk Streaming", "upgrade_tooltip": "Paket Preview tidak mengizinkan streaming. Upgrade untuk mulai streaming.", "no_slots_available": "Slot Tidak Tersedia", "streaming_limit_reached": "Anda telah mencapai batas streaming {max} stream bersamaan.", "new_stream": "Stream Baru", "create_stream_unlimited": "Buat stream baru (Slot tidak terbatas)", "create_stream_slots": "Buat stream baru. Anda memiliki {available} slot tersedia.", "upgrade_modal": {"title": "Fitur Streaming Tersedia di Paket Premium", "message": "Fitur streaming tidak termasuk dalam Paket Preview Anda saat ini. Upgrade untuk membuka fitur ini dan banyak lagi!", "cta": "<PERSON><PERSON>"}}, "streams": {"title": "Ju<PERSON>l <PERSON>ing", "description": "<PERSON><PERSON><PERSON><PERSON>", "platform": "Platform", "stream_key": "Stream Key", "video_file": "File Video", "quality": "<PERSON><PERSON><PERSON>", "orientation": "Orientasi", "horizontal": "Horizontal", "vertical": "Vertikal", "custom": "Kustom", "youtube": "YouTube", "facebook": "Facebook", "tiktok": "TikTok", "instagram": "Instagram", "twitch": "Twitch", "create_new": "Buat Streaming Baru", "edit_stream": "Edit Streaming", "delete_stream": "Hapus Streaming", "start_stream": "<PERSON><PERSON>", "stop_stream": "Hentikan Streaming", "stream_url": "URL Streaming", "rtmp_url": "URL RTMP", "advanced_settings": "Pengaturan Lanju<PERSON>", "schedule_stream": "<PERSON><PERSON><PERSON><PERSON> Streaming", "auto_stop": "<PERSON><PERSON><PERSON><PERSON>", "loop_video": "Putar Ulang Video", "bitrate": "Bitrate", "framerate": "Frame Rate", "resolution": "<PERSON><PERSON><PERSON><PERSON>"}, "gallery": {"title": "Galeri Video", "subtitle": "Kelola video Anda untuk streaming", "total_videos": "Total Video", "total_videos_desc": "Total video yang tersim<PERSON> di gallery Anda", "upload_video": "Unggah Video", "import_from_drive": "Impor dari Drive", "my_videos": "Video Saya", "video_name": "<PERSON><PERSON>", "file_size": "Ukuran File", "duration": "<PERSON><PERSON><PERSON>", "upload_date": "<PERSON><PERSON>", "no_videos": "Tidak ada video ditemukan", "no_videos_yet": "Belum ada video", "upload_first_video": "Unggah video pertama Anda untuk memulai", "upload_new": "Unggah Video Baru", "delete_video": "Hapus Video", "video_info": "Informasi Video", "supported_formats": "Format yang didukung: MP4, MOV (dioptimalkan untuk performa)", "supported_formats_short": "Format yang didukung: MP4, MOV saja", "max_file_size": "Ukuran file maksimum", "upload_progress": "Progress Unggah", "processing": "Memproses video...", "search_videos": "<PERSON><PERSON> video...", "newest": "Terbaru", "oldest": "Terlama", "showing_videos": "Menampilkan {{start}}-{{end}} dari {{total}} video", "drag_drop_files": "Seret dan lepas file video di sini", "click_to_browse": "Atau klik untuk menjelajah", "select_files": "Pilih file", "uploading": "Mengunggah...", "google_drive_import": "Impor Google Drive", "connect_google_drive": "Hubungkan Google Drive", "api_key_needed": "API key dip<PERSON><PERSON><PERSON> untuk mengakses video Drive", "enter_api_key": "Masukkan API key Google Drive Anda", "get_api_key": "Dapatkan API key <PERSON><PERSON> dari", "google_cloud_console": "Google Cloud Console", "tutorial": "Tutorial", "save_api_key": "Simpan API Key", "import_from_google_drive": "Impor dari Google Drive", "enter_drive_link": "Masukkan link Google Drive ke video Anda", "drive_link_placeholder": "https://drive.google.com/file/d/...", "make_sure_shared": "Pastikan file dibagikan dengan", "anyone_with_link": "Siapa saja dengan link", "import_video": "Impor Video", "processing_import": "Memproses...", "no_videos_search": "Tidak ada video ditemukan", "adjust_search": "Coba sesuaikan kriteria pencarian Anda"}, "history": {"title": "Riwayat Streaming", "subtitle": "<PERSON><PERSON> dan kelola streaming masa lalu <PERSON>", "search_placeholder": "Cari riwayat...", "all_platforms": "Semua Platform", "stream_name": "Nama Streaming", "start_time": "<PERSON><PERSON><PERSON>", "end_time": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "status": "Status", "platform": "Platform", "no_history": "Tidak ada riwayat streaming", "completed_streams_appear": "Streaming yang telah selesai akan muncul di sini", "view_details": "<PERSON><PERSON>", "export_history": "Ekspor Riwayat", "delete_confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus riwayat untuk \"{title}\"?", "delete_success": "Ri<PERSON><PERSON> ber<PERSON><PERSON>", "delete_error": "Error", "delete_error_general": "<PERSON><PERSON> men<PERSON><PERSON> riwayat", "no_matching_results": "Tidak ada hasil yang cocok", "adjust_search_filter": "Coba sesuaikan pencarian atau filter <PERSON>a"}, "subscription": {"title": "<PERSON><PERSON>", "current_plan": "<PERSON><PERSON>", "upgrade_plan": "Upgrade Paket", "downgrade_plan": "Downgrade Paket", "billing_cycle": "<PERSON><PERSON><PERSON>", "monthly": "Bulanan", "yearly": "<PERSON><PERSON><PERSON>", "features": "<PERSON><PERSON>", "streaming_slots": "Slot Streaming", "storage_limit": "Batas <PERSON>", "support_level": "Level Dukungan", "price": "<PERSON><PERSON>", "free": "<PERSON><PERSON><PERSON>", "basic": "Basic", "premium": "Premium", "enterprise": "Enterprise", "choose_plan": "<PERSON><PERSON><PERSON>", "choose_plan_title": "<PERSON><PERSON><PERSON>", "choose_plan_subtitle": "<PERSON><PERSON><PERSON> paket yang sempurna untuk kebutuhan streaming Anda", "streaming_usage": "Streaming", "storage_usage": "Penyimpanan", "slots_used": "slot digunakan", "used": "digunakan", "expires": "<PERSON><PERSON><PERSON>", "most_popular": "<PERSON><PERSON>", "unlimited": "Tidak Terbatas", "streaming_slot": "Slot Streaming", "storage": "Penyimpanan", "basic_features": "<PERSON><PERSON>", "get_started": "<PERSON><PERSON>", "upgrade": "Upgrade", "subscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "change_plan": "Ganti Plan", "upgrade_plan_btn": "Upgrade Plan", "downgrade_not_available": "Downgrade (Tidak Tersedia)", "faq_title": "<PERSON><PERSON><PERSON> yang <PERSON>", "faq_change_plan_q": "<PERSON><PERSON><PERSON><PERSON> saya upgrade paket kapan saja?", "faq_change_plan_a": "<PERSON>, <PERSON><PERSON> upgrade ke paket yang lebih tinggi kapan saja. Perubahan berlaku segera setelah konfirmasi pembayaran.", "faq_exceed_limits_q": "Apa yang terjadi jika saya melebihi batas?", "faq_exceed_limits_a": "Jika Anda mencapai batas streaming atau penyimpanan, <PERSON><PERSON> perlu upgrade paket atau mengosongkan ruang untuk melanjutkan menggunakan layanan.", "faq_subscription_management_q": "<PERSON>gai<PERSON> cara mengelola berl<PERSON>an saya?", "faq_subscription_management_a": "<PERSON><PERSON> da<PERSON>t upgrade paket kapan saja melalui halaman berlangganan. <PERSON>tuk perubahan berlangganan lainnya atau dukungan, silakan hubungi tim layanan pelanggan kami.", "subscribe_success": "<PERSON><PERSON><PERSON><PERSON> berl<PERSON> paket!", "subscribe_error": "Error", "subscribe_failed": "<PERSON><PERSON> be<PERSON>. Silakan coba lagi.", "trial_active": "Trial Aktif", "trial_access": "Akses <PERSON>", "trial_expires": "Trial berakhir", "current": "Saat Ini", "popular": "Populer", "best_value": "<PERSON><PERSON>"}, "settings": {"title": "<PERSON><PERSON><PERSON><PERSON>", "profile": "Pengaturan Profil", "account": "<PERSON><PERSON><PERSON><PERSON>", "security": "<PERSON><PERSON><PERSON>", "preferences": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "Pengatura<PERSON>", "profile_picture": "Foto Profil", "change_password": "Ubah Kata Sandi", "current_password": "Kata Sandi Sa<PERSON> In<PERSON>", "new_password": "<PERSON><PERSON>", "confirm_new_password": "Konfirmasi <PERSON>", "update_profile": "<PERSON><PERSON><PERSON> Profil", "delete_account": "Hapus Akun", "language_preference": "Preferensi Bahasa", "timezone": "Zona Waktu", "email_notifications": "Notifikasi <PERSON>", "push_notifications": "<PERSON><PERSON><PERSON><PERSON>", "stream_notifications": "Notifikasi Streaming", "security_notifications": "<PERSON><PERSON><PERSON><PERSON>"}, "admin": {"title": "Dashboard Admin", "subtitle": "<PERSON><PERSON><PERSON><PERSON> sistem dan manajemen", "welcome": "Selamat datang, {{username}}", "users": "Pengguna", "plans": "<PERSON><PERSON>", "performance": "Performa", "notifications": "Notif<PERSON><PERSON>", "load_balancer": "<PERSON><PERSON>r", "system_stats": "Statistik Sistem", "user_management": "<PERSON><PERSON><PERSON><PERSON>", "plan_management": "<PERSON><PERSON><PERSON><PERSON>", "create_user": "Buat Pengguna", "delete_user": "Hapus <PERSON>", "delete_users": "Hapus <PERSON>", "activate_user": "Aktifkan Pengguna", "deactivate_user": "Nonaktifkan Pengguna", "delete_plan": "Hapus Pak<PERSON>", "total_users": "Total Pengguna", "active_users": "Pengguna Aktif", "total_streams": "Total Streaming", "live_streams": "Live Streaming", "total_videos": "Total Video", "active_subscriptions": "Berlangganan Aktif", "server_load": "Beban Server", "memory_usage": "<PERSON><PERSON><PERSON><PERSON>", "cpu_usage": "Penggunaan CPU", "disk_usage": "Penggunaan <PERSON>", "network_usage": "<PERSON><PERSON><PERSON><PERSON>", "active_stat": "aktif", "total": "total", "used": "digunakan", "cores": "core", "recent_users": "Pengguna Terbaru", "subscription_plans": "<PERSON><PERSON>", "view_all": "<PERSON><PERSON>", "manage": "<PERSON><PERSON><PERSON>", "no_email": "Tidak ada email", "subscribers": "Pelanggan", "slots": "slot", "unlimited": "Tidak Terbatas", "manage_users": "<PERSON><PERSON><PERSON>", "manage_users_desc": "<PERSON><PERSON> dan kelola akun pengguna", "subscription_plans_desc": "Kon<PERSON><PERSON><PERSON><PERSON> harga dan fitur", "analytics": "<PERSON><PERSON><PERSON>", "analytics_desc": "<PERSON>hat statistik detail", "load_balancer_desc": "<PERSON><PERSON><PERSON>ian kualitas otomatis", "performance_desc": "Pantau performa sistem dan optimisasi", "stream_monitor": "Monitor Stream", "stream_monitor_desc": "Monitor dan kont<PERSON> proses FFmpeg", "user_management_title": "<PERSON><PERSON><PERSON><PERSON>", "user_management_subtitle": "<PERSON><PERSON><PERSON> akun pen<PERSON>, peran, dan izin", "select_action": "<PERSON><PERSON><PERSON>", "activate_users": "Aktifkan Pengguna", "deactivate_users": "Nonaktifkan Pengguna", "change_plan": "Ubah Paket", "apply": "Terapkan", "clear": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "Segarkan", "admins": "Admin", "premium_users": "Pengguna Premium", "all_users": "<PERSON><PERSON><PERSON>", "search_users": "<PERSON>i pengguna...", "user": "Pengguna", "role": "<PERSON><PERSON>", "plan": "<PERSON><PERSON>", "storage": "Penyimpanan", "status": "Status", "actions": "<PERSON><PERSON><PERSON>", "user_active": "Aktif", "inactive": "Tidak Aktif", "edit_user": "<PERSON>", "username": "<PERSON><PERSON>", "email": "Email", "max_streaming_slots": "Slot Streaming Maksimal", "max_storage": "Penyimpanan <PERSON>", "use_unlimited": "Gunakan -1 untuk tidak terbatas", "choose_mb_smaller": "Pilih MB untuk batas penyimpanan yang lebih kecil (mis. 500MB)", "account_active": "Akun Aktif", "reset_password": "Reset <PERSON>", "leave_empty_password": "Biarkan kosong untuk mempertahankan kata sandi saat ini", "generate": "Generate", "update_user": "<PERSON><PERSON><PERSON>", "mb_used": "MB digunakan", "gb_used": "GB digunakan", "subscription_plans_title": "<PERSON><PERSON>", "subscription_plans_subtitle": "<PERSON><PERSON><PERSON> paket berlangganan dan harga", "activate_plans": "Aktifkan Paket", "deactivate_plans": "Nonaktifkan Paket", "delete_plans": "Hapus Pak<PERSON>", "add_plan": "Tambah Paket", "most_popular": "<PERSON><PERSON>", "streaming_slot": "Slot Streaming", "streaming_slots": "Slot Streaming", "basic_support": "<PERSON><PERSON><PERSON><PERSON>", "active_subscribers": "Pelanggan Aktif", "edit": "Edit", "plan_details": "Detail Paket", "plan_name": "<PERSON><PERSON>", "price": "<PERSON><PERSON>", "add_new_plan": "Tambah Paket Baru", "currency": "<PERSON>", "billing_period": "<PERSON><PERSON>", "monthly": "Bulanan", "yearly": "<PERSON><PERSON><PERSON>", "use_zero_free": "Gunakan 0 untuk paket gratis", "use_unlimited_disable": "Gunakan -1 untuk tidak terbatas, 0 untuk menonaktifkan streaming", "use_zero_disable_storage": "Gunakan 0 untuk menonaktifkan penyimpanan, pilih MB untuk batas yang lebih kecil (mis. 500MB)", "features": "<PERSON><PERSON>", "enter_feature": "<PERSON><PERSON><PERSON><PERSON> fitur", "create_plan": "<PERSON><PERSON><PERSON> Pak<PERSON>", "edit_plan": "<PERSON>", "update_plan": "<PERSON><PERSON><PERSON>", "plan_subscribers": "Pelanggan Paket", "loading_subscribers": "<PERSON><PERSON><PERSON> pelanggan...", "close": "<PERSON><PERSON><PERSON>", "no_subscribers_found": "Tidak ada pelanggan ditemukan untuk paket ini", "delete_plan_confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus paket ini? Tindakan ini tidak dapat dibatalkan.", "per": "per", "subscription_management": "<PERSON><PERSON><PERSON><PERSON>", "subscription_management_desc": "<PERSON><PERSON><PERSON> be<PERSON>an pengguna dan penagihan", "subscriptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create_subscription": "<PERSON><PERSON><PERSON>", "extend_subscription": "<PERSON><PERSON><PERSON><PERSON>", "cancel_subscription": "Batalkan <PERSON>", "subscription_history": "Riwayat Berlangganan", "start_date": "<PERSON><PERSON>", "end_date": "<PERSON><PERSON>", "payment_method": "<PERSON><PERSON>", "subscription_status": "Status", "extension_days": "<PERSON>", "custom_end_date": "<PERSON><PERSON> Be<PERSON>", "use_prorated": "<PERSON><PERSON><PERSON>", "admin_manual": "Manual Admin", "subscription_created": "<PERSON><PERSON><PERSON><PERSON><PERSON> ber<PERSON>il dibuat", "subscription_extended": "<PERSON><PERSON><PERSON><PERSON><PERSON> ber<PERSON>", "subscription_cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>", "plan_updated": "<PERSON><PERSON> be<PERSON>", "view_history": "Lihat Riwayat", "extend": "Perpanjang", "cancel": "Batalkan", "sub_active": "Aktif", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "upgraded": "Ditingkatkan", "performance_monitoring": "Pemantauan Performa", "performance_monitoring_subtitle": "Pantau performa sistem, efisiensi cache, dan optimisasi database", "refresh_data": "Segarkan Data", "clear_cache": "<PERSON><PERSON><PERSON><PERSON>", "avg_response_time": "<PERSON><PERSON><PERSON>-rata", "milliseconds": "milide<PERSON>k", "cache_hit_rate": "<PERSON>g<PERSON>", "entries": "entri", "db_queries": "Query DB", "slow_queries": "query lambat", "error_rate": "<PERSON><PERSON><PERSON>rror", "requests": "permin<PERSON><PERSON>", "performance_alerts": "Peringatan Performa", "performance_recommendations": "Rekomendasi Performa", "suggestion": "Saran", "system_metrics": "<PERSON><PERSON>", "hours_uptime": "Jam Uptime", "active_streams": "Stream Aktif", "database_performance": "Performa Database", "avg_query_time": "<PERSON><PERSON><PERSON> (ms)", "database_size": "Ukuran Database (MB)", "tables_indexes": "Tabel & Indeks", "indexes": "indeks", "cache_performance": "<PERSON><PERSON><PERSON>", "cache_hits": "<PERSON>", "cache_misses": "<PERSON><PERSON>", "cache_entries": "<PERSON><PERSON>", "cache_usage": "<PERSON><PERSON><PERSON><PERSON>", "recent_performance_history": "Riwayat Performa Terbaru", "response_times_last_10": "<PERSON><PERSON><PERSON> (10 permintaan terakhir)", "database_query_times_last_10": "Waktu Query Database (10 query terakhir)", "clear_cache_confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin membersihkan semua cache? Ini mungkin memperlambat aplikasi sementara.", "cache_cleared_success": "<PERSON>ache ber<PERSON>il di<PERSON>!", "failed_clear_cache": "Gagal <PERSON><PERSON><PERSON> cache", "admin_notifications": "<PERSON><PERSON><PERSON><PERSON>", "admin_notifications_subtitle": "<PERSON><PERSON><PERSON> notifikasi sistem dan peringatan", "mark_all_read": "Tandai Semua Dibaca", "cleanup_old": "<PERSON><PERSON><PERSON><PERSON>", "test_notification": "Notifikasi <PERSON>", "create_samples": "Buat Sampel", "total_notifications": "Total Notifikasi", "unread": "Belum Dibaca", "critical": "<PERSON><PERSON><PERSON>", "system_alerts": "Peringatan Sistem", "filters": "Filter", "type": "Tipe", "all_types": "<PERSON><PERSON><PERSON>", "info": "Info", "warning": "Peringatan", "error": "Error", "success": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "all_categories": "<PERSON><PERSON><PERSON>", "system": "Sistem", "streams": "Stream", "priority": "Prioritas", "all_priorities": "<PERSON><PERSON><PERSON>", "low": "Rendah", "normal": "Normal", "high": "Tingg<PERSON>", "all": "<PERSON><PERSON><PERSON>", "read": "Dibaca", "loading_notifications": "Me<PERSON>at notifika<PERSON>...", "no_notifications_found": "Tidak ada notifikasi ditemukan", "create_test_notification": "Buat Notifikasi Uji", "message": "<PERSON><PERSON>", "test_notification_title": "Notifikasi <PERSON>", "test_notification_message": "Ini adalah pesan notifikasi uji.", "create": "Buat", "mark_all_read_confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menandai semua notifikasi sebagai sudah dibaca?", "cleanup_old_confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus notifikasi lama (lebih dari 30 hari)?", "create_samples_confirm": "Ini akan membuat 5 notifikasi sampel. Lanjutkan?", "delete_notification_confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus notifikasi ini?", "mark_as_read": "Tandai sudah dibaca", "delete_notification": "<PERSON><PERSON> notif<PERSON>", "give_trial": "Berikan Trial", "remove_trial": "Hapus Trial", "trial_duration": "Durasi Trial", "trial_duration_days": "<PERSON><PERSON><PERSON> (<PERSON>)", "trial_slots": "Slot Trial", "trial_storage": "Storage Trial (MB)", "give_trial_confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin memberikan akses trial kepada pengguna ini?", "remove_trial_confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus akses trial dari pengguna ini?", "trial_given_success": "Akses trial ber<PERSON><PERSON>ber<PERSON>n", "trial_removed_success": "Aks<PERSON> trial ber<PERSON><PERSON> di<PERSON>pus", "user_has_active_trial": "Pengguna sudah memiliki trial aktif", "trial_expires_on": "Trial be<PERSON>hir pada", "active_trial": "Trial Aktif", "marked_notifications_as_read": "notifikasi ditandai sudah dibaca", "failed_to_mark_as_read": "Gagal menandai notifikasi sebagai sudah dibaca", "error_marking_as_read": "Error menandai notifikasi sebagai sudah dibaca", "failed_to_cleanup": "Gagal <PERSON><PERSON><PERSON>", "error_cleaning_up": "Error <PERSON><PERSON><PERSON>", "test_notification_created": "Notifikasi uji ber<PERSON><PERSON> dibuat", "failed_to_create_test": "<PERSON>l membuat notifikasi uji", "error_creating_test": "Error membuat notifikasi uji", "failed_to_create_samples": "Gagal membuat notifikasi sampel", "error_creating_samples": "Error membuat notifikasi sampel", "notification_deleted": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON><PERSON>", "failed_to_delete": "<PERSON><PERSON> not<PERSON>", "error_deleting": "<PERSON><PERSON><PERSON> not<PERSON>", "page": "<PERSON><PERSON>", "failed_to_load": "<PERSON>l memuat notifikasi", "error_loading": "<PERSON>rror memuat notif<PERSON>", "unable_to_connect": "Tidak dapat terhubung ke server. Silakan periksa apakah server sedang berjalan.", "stream_status_sync": "Sinkronisasi Status Stream", "stream_status_sync_desc": "Pantau dan sinkronkan status stream antara database dan proses aktif", "sync_now": "Sinkronkan Sekarang", "database_live": "Database Live", "memory_active": "Memori Aktif", "inconsistencies": "Inkonsistensi"}, "notifications": {"title": "Notif<PERSON><PERSON>", "mark_all_read": "Tandai semua sudah dibaca", "view_all": "<PERSON><PERSON>a", "no_notifications": "Tidak ada notifikasi", "stream_started": "Streaming dimulai", "stream_stopped": "Streaming dihentikan", "stream_error": "Error streaming", "upload_complete": "<PERSON><PERSON><PERSON>", "upload_failed": "<PERSON><PERSON><PERSON> gagal", "quota_exceeded": "Kuota terlampaui", "plan_upgraded": "<PERSON><PERSON>", "plan_downgraded": "<PERSON><PERSON>", "payment_success": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>", "payment_failed": "<PERSON><PERSON><PERSON><PERSON> gagal", "new_notification_received": "Notifikasi baru diterima"}, "errors": {"general_error": "<PERSON><PERSON><PERSON><PERSON>", "network_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>", "server_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON> server", "not_found": "Halaman tidak ditemukan", "unauthorized": "<PERSON><PERSON><PERSON> t<PERSON>", "forbidden": "<PERSON><PERSON><PERSON>", "validation_error": "<PERSON><PERSON><PERSON> validasi", "file_too_large": "Ukuran file terlalu besar", "invalid_file_type": "Tipe file tidak valid", "quota_exceeded": "Kuota penyimpanan terlampaui", "stream_limit_reached": "Batas streaming tercapai", "invalid_stream_key": "Stream key tidak valid", "stream_not_found": "Streaming tidak ditemukan", "video_not_found": "Video tidak ditemukan", "user_not_found": "Pengguna tidak ditemukan", "plan_not_found": "<PERSON><PERSON> tidak di<PERSON>n"}, "success": {"stream_created": "Streaming berhasil dibuat", "stream_updated": "Streaming berhasil diperbarui", "stream_deleted": "Streaming berhasil dihapus", "stream_started": "Streaming berhasil dimulai", "stream_stopped": "Streaming berhasil dihentikan", "video_uploaded": "Video berhasil diunggah", "video_deleted": "Video berhasil dihapus", "profile_updated": "<PERSON>il be<PERSON><PERSON><PERSON>", "password_changed": "<PERSON>a sandi be<PERSON><PERSON><PERSON>h", "settings_saved": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON><PERSON> disimpan", "plan_updated": "<PERSON><PERSON> be<PERSON>", "notification_sent": "Not<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>"}, "referral": {"title": "Program Referral", "subtitle": "<PERSON><PERSON> teman dan dapatkan komisi 5% dari setiap pembelian plan", "dashboard": "Dashboard Referral", "my_referrals": "Referral Saya", "referral_link": "Referral Link", "referral_code": "Kode Referral", "copy_link": "<PERSON><PERSON>", "copy_code": "<PERSON><PERSON>", "share_link": "Bagikan link ini kepada teman-teman Anda untuk mendapatkan komisi ketika mereka berlangganan", "balance": "<PERSON>do <PERSON>", "total_earnings": "Total Komisi", "pending_referrals": "Referral Pending", "successful_referrals": "Referral Berhasil", "commission_rate": "<PERSON><PERSON><PERSON>", "withdraw_balance": "<PERSON><PERSON>", "minimum_withdrawal": "Minimum Penarikan", "withdrawal_amount": "<PERSON><PERSON><PERSON>", "bank_name": "Nama Bank", "account_number": "Nomor <PERSON>", "account_name": "<PERSON><PERSON> Pem<PERSON>k <PERSON>", "submit_withdrawal": "<PERSON><PERSON><PERSON>", "withdrawal_history": "Riwayat Penarikan", "earnings_history": "Riwayat Komisi", "referral_stats": "Statistik Referral", "total_clicks": "Total Klik", "total_signups": "Total Pendaftaran", "conversion_rate": "Ting<PERSON>nversi", "commission_from": "<PERSON><PERSON><PERSON> dari", "no_earnings": "Belum ada komisi yang diterima", "share_referral_link": "Bagikan link referral Anda untuk mulai mendapatkan komisi", "no_pending_referrals": "Tidak ada referral pending", "share_for_more_signups": "Bagikan link referral untuk mendapatkan lebih banyak signup", "no_successful_referrals": "Belum ada referral ber<PERSON>il", "wait_for_purchases": "<PERSON><PERSON><PERSON> hingga referral <PERSON><PERSON> me<PERSON> plan", "no_withdrawal_history": "Belum ada riwayat penarikan", "withdrawal_history_appear": "Riwayat penarikan saldo akan muncul di sini", "loading_data": "Memuat data...", "refresh": "Refresh", "cancel": "<PERSON><PERSON>", "processing": "Memproses...", "success": "Ber<PERSON>il!", "referral_code_copied": "Kode referral ber<PERSON><PERSON> disalin!", "referral_link_copied": "Link referral berhasil disalin!", "withdrawal_submitted": "Permintaan penarikan berhasil diajukan!", "withdrawal_failed": "Gagal mengajukan penarikan", "withdrawal_error": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mengajukan penarikan", "earnings_updated": "Riwayat komisi ber<PERSON>er<PERSON>", "earnings_load_failed": "Gagal memuat riwayat komisi", "referral_details_failed": "Gagal memuat detail referral", "withdrawal_history_failed": "Gagal memuat riwayat penarikan", "try_again": "<PERSON><PERSON>", "not_purchased_plan": "Belum beli plan", "status": {"pending": "Pending", "approved": "Disetuju<PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "completed": "Se<PERSON><PERSON>"}, "placeholders": {"minimum_amount": "Minimum Rp 50.000", "bank_example": "Contoh: Bank BCA", "account_number_placeholder": "Nomor rekening bank", "account_name_placeholder": "Nama sesuai rekening bank"}, "validation": {"amount_required": "<PERSON><PERSON><PERSON> penarikan wajib diisi", "bank_required": "Nama bank wajib diisi", "account_number_required": "Nomor rekening wajib diisi", "account_name_required": "<PERSON><PERSON> pemilik rekening wajib diisi"}, "how_it_works": {"title": "Cara Kerja Program Referral", "description": "Dengan mengaktifkan program referral, <PERSON><PERSON> akan mendapatkan kode unik yang dapat dibagikan kepada teman-teman Anda. Dapatkan komisi <strong>5%</strong> dari <strong>pembelian pertama</strong> setiap teman yang berlangganan menggunakan kode referral Anda.", "note": "Komisi hanya diberikan untuk pembelian pertama setiap referral, tidak untuk upgrade atau perpanjangan selanjutnya."}, "available_balance": "<PERSON><PERSON> tersedia", "total_link_clicks": "Total klik link", "processing_info": "Penarikan akan diproses secara manual oleh admin dalam 1-3 hari kerja.", "loading_withdrawal_history": "Memuat riwayat penarikan...", "your_referral_code": "Kode Referral Anda", "minimum_withdrawal_info": "Minimum penarikan adalah Rp 50.000"}, "admin_referral": {"title": "Referral Settlements", "subtitle": "<PERSON><PERSON><PERSON> permintaan penarikan saldo referral", "withdrawal_requests": "<PERSON><PERSON><PERSON><PERSON>", "total_referrals": "Total Referrals", "total_referrals_desc": "Total referral dibuat", "completed_referrals": "Completed", "completed_referrals_desc": "Referral berhasil", "total_commissions": "Total Komisi", "total_commissions_desc": "Total komisi dibayar", "all_status": "Semua Status", "user": "User", "amount": "<PERSON><PERSON><PERSON>", "bank": "Bank", "account": "Rekening", "status": "Status", "date": "Tanggal", "actions": "<PERSON><PERSON><PERSON>", "no_email": "Tidak ada email", "approve": "Approve", "reject": "Reject", "processed": "Processed", "note": "Note", "no_withdrawal_requests": "Belum ada permintaan penarikan", "withdrawal_requests_appear": "Permintaan penarikan dari user akan muncul di sini", "process_withdrawal": "Process Withdrawal", "approve_withdrawal": "Approve Withdrawal", "reject_withdrawal": "Reject <PERSON>", "admin_notes": "Cat<PERSON><PERSON> (Opsional)", "admin_notes_placeholder": "Tambahkan catatan untuk user...", "processing_request": "Memproses...", "withdrawal_approved": "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>", "withdrawal_rejected": "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>", "withdrawal_process_failed": "Gagal memproses penarikan", "withdrawal_process_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat memproses penarikan", "refresh": "Refresh"}, "landing": {"nav": {"how_it_works": "<PERSON>", "features": "<PERSON><PERSON>", "video_tutorial": "Tutorial Video", "pricing": "<PERSON><PERSON>", "faq": "FAQ", "support": "Dukungan", "login": "<PERSON><PERSON><PERSON>", "get_started": "<PERSON><PERSON>"}, "hero": {"title": "Platform Streaming Otomatis", "subtitle": "Ubah konten Anda menjadi siaran langsung berkelanjutan di berbagai platform dengan solusi streaming berbasis cloud kami.", "cta_primary": "<PERSON><PERSON>", "cta_secondary": "<PERSON><PERSON><PERSON><PERSON>", "streaming_to": "Menyiark<PERSON> ke:", "stats": {"uptime": "99.9%", "uptime_label": "<PERSON><PERSON><PERSON>", "streaming": "24<span class=\"slash-fix\"></span>7", "streaming_label": "Streaming Berkelanjutan", "platforms": "5+", "platforms_label": "Platform"}}, "features": {"title": "<PERSON><PERSON>", "subtitle": "Alat-alat canggih yang dirancang untuk penyiaran konten yang mulus", "feature_1": {"title": "Penyiaran Konten Berkelanjutan", "desc": "Streaming video Anda secara berkelanjutan ke berbagai platform menggunakan infrastruktur cloud kami, menghilangkan kebutuhan perangkat keras khusus."}, "feature_2": {"title": "Distribusi Multi-Platform", "desc": "<PERSON><PERSON><PERSON> secara be<PERSON>an ke YouTube, Facebook, Twitch, TikTok, dan <PERSON>sta<PERSON> dari satu panel kontrol terpadu."}, "feature_3": {"title": "Sistem <PERSON>", "desc": "Program pengiriman konten Anda dengan penjadwalan cerdas yang secara otomatis mengelola timeline streaming Anda."}, "feature_4": {"title": "Panel Kontrol Intuitif", "desc": "Ke<PERSON>la semua aktivitas streaming Anda melalui dashboard yang dirancang khusus untuk kreator konten dari semua tingkat keahlian."}, "feature_5": {"title": "Infrastruktur Tingkat Enterprise", "desc": "Manfaatkan server cloud yang kuat yang memberikan performa konsisten dengan jaminan uptime terdepan di industri."}, "feature_6": {"title": "<PERSON>", "desc": "<PERSON><PERSON><PERSON> tim dukungan ahli kami melalui berbagai saluran kapan pun Anda membutuhkan bantuan dengan pengaturan streaming."}}, "how_it_works": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Luncurkan saluran streaming Anda dalam tiga langkah sederhana", "step_1": {"title": "Pengaturan & Upload Konten", "desc": "<PERSON><PERSON><PERSON> akun <PERSON> dan upload konten video yang ingin Anda si<PERSON>an secara berkelanjutan"}, "step_2": {"title": "Integrasi Platform", "desc": "Konfigurasi tujuan streaming Anda dengan menambahkan kredensial platform dan stream key"}, "step_3": {"title": "<PERSON><PERSON><PERSON><PERSON>", "desc": "Aktifkan streaming Anda dan pantau distribusi konten dari dashboard terpusat kami"}}, "video_tutorial": {"title": "<PERSON><PERSON>", "subtitle": "Saksikan StreamOnPod beraksi dengan tutorial video komprehen<PERSON><PERSON> kami", "video_title": "Tutorial Lengkap StreamOnPod", "description": "Pelajari cara menggunakan StreamOnPod dari awal hingga akhir. Tutorial ini mencakup semua hal mulai dari pengaturan akun hingga konfigurasi streaming lanjutan.", "loading": "Memuat video...", "duration": "15 menit", "views": "Video Tutorial", "language": "Bahasa Indonesia"}, "pricing": {"title": "Paket Fleksibel", "subtitle": "<PERSON><PERSON><PERSON> solusi streaming yang sempurna untuk strategi konten <PERSON>a", "free_trial": "<PERSON><PERSON> dengan tier gratis kami", "per_month": "Bulanan", "streaming_slots": "<PERSON><PERSON><PERSON>", "storage": "Penyimpanan Konten", "basic_support": "Duku<PERSON><PERSON> Standar", "choose_free": "<PERSON><PERSON>", "choose_plan": "<PERSON><PERSON><PERSON>"}, "faq": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> yang perlu Anda ketahui tentang platform streaming kami", "q1": {"question": "<PERSON><PERSON><PERSON><PERSON> diperlukan perangkat keras khusus untuk streaming berkelanjutan?", "answer": "Sama sekali tidak! Infrastruktur berbasis cloud kami menangani semua beban kerja berat, memungkinkan Anda men<PERSON>an secara berkelanjutan tanpa persyaratan perangkat keras lokal."}, "q2": {"question": "<PERSON><PERSON><PERSON><PERSON> saya men<PERSON>an ke beberapa platform sekaligus?", "answer": "Tentu saja! Platform kami mendukung penyiaran simultan ke YouTube, Facebook, Twitch, TikTok, dan Instagram melalui antarmuka terpadu."}, "q3": {"question": "Tingkat keahlian teknis apa yang diperlukan?", "answer": "Tidak ada sama sekali. Platform kami dibangun dengan kesederhanaan dalam pikiran, menampilkan antarmuka intuitif yang dapat dikuasai siapa pun dengan cepat."}, "q4": {"question": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>n konten otomatis tersedia?", "answer": "Ya! Sistem penjadwalan cerdas kami memungkinkan Anda merenc<PERSON>kan kalender konten dan mengotomatisasi timeline penyiaran dengan presisi."}, "q5": {"question": "<PERSON>gai<PERSON> Anda menangani masalah teknis?", "answer": "<PERSON>n khusus kami menyed<PERSON>kan bantuan komp<PERSON><PERSON><PERSON><PERSON> melalui berb<PERSON>i saluran, memastikan operasi streaming <PERSON>a berjalan lancar setiap saat."}}, "testimonials": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Dengar dari kreator konten yang telah mentransformasi alur kerja <PERSON> mereka", "testimonial_1": {"name": "<PERSON>", "role": "<PERSON><PERSON>", "text": "StreamOnPod merevolusi strategi konten saya. Pen<PERSON>dwalan otomatis membebaskan jam-jam waktu saya untuk fokus membuat konten yang lebih baik."}, "testimonial_2": {"name": "<PERSON>", "role": "Gaming Streamer", "text": "Fitur penyiaran multi-platform sangat luar biasa. Saya bisa menjangkau audiens di semua platform utama secara bersamaan tanpa kerumitan teknis."}, "testimonial_3": {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON>", "text": "Keandalan<PERSON> luar biasa. Stream edukasi saya ber<PERSON>lan terus-<PERSON><PERSON> tanpa gangguan, memberikan nilai konsisten kepada audiens saya."}}, "cta": {"title": "Siap Mentransformasi Strategi Konten Anda?", "subtitle": "Bergabunglah dengan komunitas kreator yang berkembang yang telah merampingkan penyiaran mereka dengan platform cerdas StreamOnPod.", "start_streaming": "Luncurkan Saluran Anda", "contact_support": "Dapatkan Ban<PERSON>"}, "footer": {"description": "Platform streaming bertenaga cloud yang memungkinkan penyiaran konten berkelanjutan di berbagai platform melalui otomasi cerdas dan infrastruktur tingkat enterprise.", "quick_links": "Na<PERSON><PERSON><PERSON>", "support_title": "Bantuan & Dukungan", "help_center": "Pusat Du<PERSON>", "contact_us": "<PERSON><PERSON><PERSON><PERSON>", "copyright": "Dibuat dengan ❤️ untuk komunitas kreator.", "terms_of_service": "Syarat & Ketentuan", "privacy_policy": "<PERSON><PERSON><PERSON><PERSON>"}}, "privacy": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Kebijakan Privasi StreamOnPod", "intro": "StreamOnPod (\"kami\", \"kita\", \"milik kami\") berkomitmen untuk melindungi dan menghormati privasi Anda. Kebijakan Privasi ini menjelaskan bagaimana kami mengum<PERSON>, men<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dan melindungi informasi pribadi Anda saat menggunakan layanan kami.", "consent": "<PERSON>gan mengakses atau menggunakan layanan StreamOnPod, <PERSON><PERSON> men<PERSON>i praktik yang dijelaskan dalam Kebijakan Privasi ini.", "section_1": {"title": "Informasi yang <PERSON>", "intro": "<PERSON><PERSON> dapat mengumpulkan informasi berikut:", "content": ["Informasi Identitas Pribadi (contoh: nama lengkap, alamat email, nomor telepon)", "Informasi Teknis (contoh: alamat IP, j<PERSON><PERSON>, browser yang diguna<PERSON>, waktu aks<PERSON>, halaman yang di<PERSON>)", "Informasi Transaksi (contoh: data pem<PERSON><PERSON>, produk atau layanan yang <PERSON>a beli)", "Informasi Komunikasi (contoh: pesan, per<PERSON><PERSON>, atau <PERSON> yang <PERSON>a kirimkan kepada kami)", "Data Penggunaan (contoh: cara <PERSON>a menggunakan layanan kami, aktivitas dalam aplikasi atau situs web)"], "note": "Kami hanya mengumpulkan data yang relevan dan diperlukan untuk menyediakan layanan terbaik kepada Anda."}, "section_2": {"title": "Cara <PERSON> Informasi Anda", "intro": "Informasi yang dikumpulkan dapat digunakan untuk:", "content": ["<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, dan memel<PERSON>ara layanan kami.", "Memproses transaksi dan mengelola akun <PERSON>.", "Mengirimkan informasi penting terkait layanan (se<PERSON>i per<PERSON>, pembaruan, atau peringatan).", "Memberikan dukungan pelanggan.", "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan me<PERSON><PERSON><PERSON> layanan kami.", "Mengirimkan promosi, penawaran, atau informasi marketing (hanya jika Anda setuju).", "Menganalisis perilaku pengguna untuk peningkatan layanan.", "Mencegah aktivitas ilegal atau pelanggaran ketentuan penggunaan."]}, "section_3": {"title": "<PERSON><PERSON><PERSON><PERSON> dan Teknologi Pelacakan", "intro": "<PERSON><PERSON> men<PERSON> cookie dan teknologi serupa untuk:", "purposes": ["Meningkatkan pengalaman pengguna.", "Mengan<PERSON><PERSON> penggunaan layanan.", "<PERSON><PERSON><PERSON><PERSON> konten yang lebih relevan."], "internal_cookies": {"title": "<PERSON><PERSON>", "description": "<PERSON>ie digunakan untuk menyimpan preferensi pengguna dan mengoptimalkan layanan yang diberikan."}, "third_party_cookies": {"title": "<PERSON><PERSON>", "intro": "Kami juga menggunakan layanan pihak ketiga berikut:", "google_analytics": {"title": "Google Analytics", "description": "Untuk membantu kami memahami bagaimana pengunjung menggunakan situs kami. Informasi yang dikumpulkan termasuk, namun tidak terbatas pada: halaman yang dikunjungi, durasi kun<PERSON>, lokasi pengguna (berdasarkan IP), dan jenis perangkat yang digunakan. Data ini digunakan untuk tujuan analisis internal dan tidak dibagikan untuk penggunaan komersial pihak lain."}, "meta_pixel": {"title": "<PERSON><PERSON> (Facebook Pixel)", "description": "Untuk mengukur efektivitas iklan kami di platform Meta (Facebook, Instagram) dan untuk menargetkan audiens yang lebih relevan. Data yang dikumpulkan dapat mencakup aktivitas di situs kami seperti kunjungan halaman dan interaksi dengan konten."}, "note": "Catatan: <PERSON><PERSON>t mengatur preferensi cookie melalui pengaturan browser <PERSON><PERSON> atau melalui pengaturan iklan masing-masing platform."}}, "section_4": {"title": "Pengungkapan Informasi kepada Pihak Ketiga", "intro": "<PERSON><PERSON> t<PERSON>, <PERSON><PERSON><PERSON><PERSON>, atau menukar informasi pribadi Anda kepada pihak ketiga. <PERSON><PERSON>, kami dapat membagikan informasi Anda dengan:", "content": ["Penyedia layanan pihak ketiga seperti penyedia hosting, pembayaran, analitik, atau email marketing.", "<PERSON><PERSON>, a<PERSON><PERSON><PERSON> diwaji<PERSON><PERSON> oleh hukum atau perintah pengadilan."], "note": "<PERSON><PERSON><PERSON> pihak ketiga tersebut terikat untuk menjaga kerahasiaan dan keamanan informasi Anda."}, "section_5": {"title": "Keamanan Data", "intro": "<PERSON><PERSON> men<PERSON> langkah-langkah teknis dan organisasi yang wajar untuk:", "content": ["Melindungi data pribadi dari aks<PERSON>, pen<PERSON><PERSON><PERSON>, atau pengungkapan yang tidak sah.", "Mencegah kehilangan, perusakan, atau perubahan data."], "disclaimer": "<PERSON><PERSON>, tidak ada sistem transmisi data melalui internet yang 100% aman. Kami berkomitmen melakukan upaya terbaik untuk melindungi data Anda."}, "section_6": {"title": "Penyimpanan dan <PERSON>", "content": ["Data pribadi Anda disimpan selama diperlukan untuk memenuhi tujuan pengumpulan atau selama diwajibkan oleh hukum yang berlaku.", "<PERSON>a dapat meminta penghapusan data Anda kapan saja, kecuali ada kewajiban hukum yang mengharuskan kami mempertahankannya."]}, "section_7": {"title": "Hak Anda sebagai Pengguna", "intro": "Anda memiliki hak untuk:", "content": ["Mengakses data pribadi Anda.", "Memperbaiki data yang tidak akurat.", "Meminta penghapusan data pribadi Anda.", "<PERSON><PERSON><PERSON> sebelumnya.", "Menolak penggunaan data untuk keperluan tertentu."], "note": "<PERSON><PERSON>an hubungi kami untuk menggunakan hak-hak ini."}, "section_8": {"title": "<PERSON><PERSON><PERSON>", "content": ["<PERSON><PERSON> dapat memper<PERSON>ui <PERSON> Privasi ini dari waktu ke waktu.", "<PERSON><PERSON>an akan diberlakukan segera setelah diposting di platform kami, dengan pemberitahuan yang sesuai jika diperlukan."]}, "section_9": {"title": "Kontak Kami", "intro": "<PERSON><PERSON> Anda memiliki per<PERSON>, per<PERSON><PERSON><PERSON>, atau keluhan terkait <PERSON>vasi ini, <PERSON>a dapat menghubungi kami di:", "email": "Email: <EMAIL>", "telegram": "Telegram: @streamonpod"}, "conclusion": "<PERSON>gan menggunakan layanan <PERSON>, <PERSON><PERSON> menya<PERSON>kan bahwa Anda telah me<PERSON>, me<PERSON><PERSON>, dan men<PERSON><PERSON><PERSON><PERSON> Privasi ini."}, "tos": {"title": "Syarat & Ketentuan", "subtitle": "Syarat & Ketentuan Penggunaan Layanan StreamOnPod", "intro": "Te<PERSON> kasih telah menggunakan layanan StreamOnPod! Dengan mengakses dan menggunakan layanan kami, <PERSON><PERSON> setuju untuk terikat dengan Syarat dan Ketentuan berikut ini. <PERSON><PERSON> baca dengan seksama.", "section_1": {"title": "Definisi", "content": {"streamonpod": "StreamOnPod: <PERSON><PERSON><PERSON> yang men<PERSON> alat, sumber daya, dan layanan untuk mendukung kebutuhan konten kreator, termasuk namun tidak terbatas pada alat streaming 24/7 dan manajemen video.", "user": "Pengguna: Individu atau entitas yang mengakses dan/atau menggunakan layanan StreamOnPod.", "service": "Layanan: <PERSON><PERSON><PERSON> produ<PERSON>, fitur, konte<PERSON>, a<PERSON><PERSON><PERSON>, atau layanan yang disediakan oleh StreamOnPod."}}, "section_2": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": ["Pengguna wajib mematuhi seluruh ketentuan yang tercantum dalam dokumen ini.", "StreamOnPod berhak men<PERSON>, menambah, atau menghapus bagian dari <PERSON> & Ketentuan ini kapan saja tanpa pemberitahuan sebelumnya.", "<PERSON>gan tetap menggunakan layanan setelah per<PERSON>han, <PERSON><PERSON> di<PERSON>p menerima perubahan tersebut."]}, "section_3": {"title": "<PERSON><PERSON>", "content": ["StreamOnPod menyediakan alat bantu dan sumber daya untuk mempermudah proses pembuatan, pen<PERSON><PERSON><PERSON>, dan <PERSON><PERSON><PERSON> konten digital.", "Produk yang dita<PERSON>an mencakup namun tidak terbatas pada:", "• Otomasi streaming 24/7", "• <PERSON><PERSON><PERSON><PERSON> dan penyi<PERSON>n file video", "• Dukungan streaming multi-platform", "• <PERSON><PERSON> pen<PERSON>n dan manajemen streaming"]}, "section_4": {"title": "Kepemilikan dan <PERSON>", "content": ["<PERSON><PERSON><PERSON>, <PERSON><PERSON>, dan alat yang disediakan oleh StreamOnPod merupakan milik StreamOnPod atau pemegang lisensi terkait.", "Pengguna tidak diperkenankan untuk memperbanyak, menjual, mendistribusikan, atau mengeksploitasi materi StreamOnPod tanpa izin tertulis.", "StreamOnPod menghormati hak kekayaan intelektual pihak lain. Ji<PERSON> Anda merasa hak <PERSON>a <PERSON>, harap hubungi kami."]}, "section_5": {"title": "Tanggung Jawab <PERSON>", "content": ["Pengguna bertanggung jawab penuh atas penggunaan layanan dan materi yang diunduh atau digunakan dari StreamOnPod.", "Pengguna wajib menggunakan layanan sesuai hukum yang berlaku di Republik Indonesia.", "Pengguna dilarang menggunakan layanan untuk tujuan ilegal, melanggar hak pihak lain, atau merugikan pihak ketiga."]}, "section_6": {"title": "Pembayaran dan <PERSON> Refund", "content": ["<PERSON><PERSON><PERSON> pem<PERSON>aran dilakukan sesuai harga yang tercantum pada saat transaksi.", "Tidak ada pengembalian dana (refund) kecuali ada kesalahan dari pihak StreamOnPod atau disepakati sebaliknya.", "StreamOnPod berhak menolak layanan kepada siapa pun atas dasar alasan yang wajar."]}, "section_7": {"title": "Batasan <PERSON>", "content": ["StreamOnPod tidak bertanggung jawab atas kerugian langsung, tidak langsung, insidental, khusus, atau konsekuensial akibat penggunaan atau ketidakmampuan menggunakan layanan.", "StreamOnPod tidak menjamin bahwa layanan akan selalu bebas dari gangguan, error, atau aman dari serangan pihak ketiga."]}, "section_8": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": ["StreamOnPod menghormati privasi pengguna. Data pribadi yang dikumpulkan akan digunakan hanya untuk keperluan operasional layanan.", "StreamOnPod tidak akan menjual atau membagikan data pengguna kepada pihak ketiga tanpa izin, kecuali diwajibkan oleh hukum."]}, "section_9": {"title": "Force Majeure", "content": ["StreamOnPod tidak bertanggung jawab atas kegagalan atau keterlambatan dalam menyediakan layanan akibat kejadian di luar kendali wajar seperti bencana alam, perang, kegagalan jaringan, atau tindakan pemerintah."]}, "section_10": {"title": "<PERSON><PERSON><PERSON> yang <PERSON>", "content": ["<PERSON>yarat dan <PERSON>an ini diatur dan ditafsirkan berdasarkan hukum Republik Indonesia.", "<PERSON><PERSON> sengketa yang timbul akan diselesaikan secara musyawarah. <PERSON><PERSON> tidak terca<PERSON>, maka akan diselesaikan melalui jalur hukum yang berlaku di Indonesia."]}, "section_11": {"title": "<PERSON><PERSON><PERSON>", "content": ["<PERSON>gan menggunakan layanan <PERSON>, <PERSON>a menya<PERSON>kan telah memba<PERSON>, me<PERSON><PERSON>, dan menyetujui semua ketentuan di atas.", "Jika Anda tidak setuju dengan <PERSON>at dan <PERSON> ini, mohon untuk tidak menggunakan layanan kami."]}, "contact": {"title": "Kontak Kami", "description": "Untuk per<PERSON>aan atau klarifikasi lebih lan<PERSON>t, silakan hubungi:", "email": "Email: <EMAIL>", "telegram": "Telegram: @streamonpod"}}}