const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs');
const { db } = require('../db/database');
class Video {
  static async create(data) {
    return new Promise((resolve, reject) => {
      const id = uuidv4();
      const now = new Date().toISOString();
      db.run(
        `INSERT INTO videos (
          id, title, filepath, thumbnail_path, file_size,
          duration, format, resolution, bitrate, fps, codec, audio_codec, user_id,
          processing_status, streaming_ready_path, original_filepath,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id, data.title, data.filepath, data.thumbnail_path, data.file_size,
          data.duration, data.format, data.resolution, data.bitrate, data.fps,
          data.codec, data.audioCodec, data.user_id,
          data.processing_status || 'pending', data.streaming_ready_path || null, data.original_filepath || null,
          now, now
        ],
        function (err) {
          if (err) {
            console.error('Error creating video:', err.message);
            return reject(err);
          }
          resolve({ id, ...data, created_at: now, updated_at: now });
        }
      );
    });
  }
  static findById(id) {
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM videos WHERE id = ?', [id], (err, row) => {
        if (err) {
          console.error('Error finding video:', err.message);
          return reject(err);
        }
        resolve(row);
      });
    });
  }
  static findAll(userId = null) {
    return new Promise((resolve, reject) => {
      const query = userId ?
        'SELECT * FROM videos WHERE user_id = ? ORDER BY upload_date DESC' :
        'SELECT * FROM videos ORDER BY upload_date DESC';
      const params = userId ? [userId] : [];
      db.all(query, params, (err, rows) => {
        if (err) {
          console.error('Error finding videos:', err.message);
          return reject(err);
        }
        resolve(rows || []);
      });
    });
  }

  static async findAndCountAll(userId, limit, offset) {
    return new Promise((resolve, reject) => {
      const countQuery = userId ? 'SELECT COUNT(*) as total FROM videos WHERE user_id = ?' : 'SELECT COUNT(*) as total FROM videos';
      const countParams = userId ? [userId] : [];

      db.get(countQuery, countParams, (err, row) => {
        if (err) {
          console.error('Error counting videos:', err.message);
          return reject(err);
        }

        const totalVideos = row.total;
        const query = userId ?
          'SELECT * FROM videos WHERE user_id = ? ORDER BY upload_date DESC LIMIT ? OFFSET ?' :
          'SELECT * FROM videos ORDER BY upload_date DESC LIMIT ? OFFSET ?';
        const params = userId ? [userId, limit, offset] : [limit, offset];

        db.all(query, params, (err, rows) => {
          if (err) {
            console.error('Error finding videos:', err.message);
            return reject(err);
          }
          resolve({ videos: rows || [], totalVideos });
        });
      });
    });
  }

  static update(id, videoData) {
    // Whitelist of allowed fields to prevent SQL injection
    const allowedFields = [
      'title', 'filepath', 'thumbnail_path', 'file_size', 'duration',
      'format', 'resolution', 'bitrate', 'fps', 'codec', 'audio_codec'
    ];

    const fields = [];
    const values = [];
    const sanitizedData = {};

    Object.entries(videoData).forEach(([key, value]) => {
      if (allowedFields.includes(key)) {
        fields.push(`${key} = ?`);
        values.push(value);
        sanitizedData[key] = value;
      } else {
        console.warn(`Video.update: Ignoring invalid field '${key}'`);
      }
    });

    if (fields.length === 0) {
      return Promise.reject(new Error('No valid fields to update'));
    }

    fields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);
    const query = `UPDATE videos SET ${fields.join(', ')} WHERE id = ?`;

    return new Promise((resolve, reject) => {
      db.run(query, values, function (err) {
        if (err) {
          console.error('Error updating video:', err.message);
          return reject(err);
        }
        resolve({ id, ...sanitizedData });
      });
    });
  }

  static async updateProcessingStatus(id, status, streamingReadyPath = null, processedMetadata = null) {
    return new Promise((resolve, reject) => {
      let query = 'UPDATE videos SET processing_status = ?, updated_at = ?';
      let params = [status, new Date().toISOString()];

      if (streamingReadyPath) {
        query += ', streaming_ready_path = ?';
        params.push(streamingReadyPath);
      }

      if (processedMetadata) {
        if (processedMetadata.resolution) {
          query += ', resolution = ?';
          params.push(processedMetadata.resolution);
        }
        if (processedMetadata.bitrate) {
          query += ', bitrate = ?';
          params.push(processedMetadata.bitrate);
        }
        if (processedMetadata.fps) {
          query += ', fps = ?';
          params.push(processedMetadata.fps);
        }
        if (processedMetadata.codec) {
          query += ', codec = ?';
          params.push(processedMetadata.codec);
        }
      }

      query += ' WHERE id = ?';
      params.push(id);

      db.run(query, params, function (err) {
        if (err) {
          console.error('Error updating video processing status:', err.message);
          return reject(err);
        }
        resolve({ success: true, changes: this.changes });
      });
    });
  }

  static async getProcessingQueue() {
    return new Promise((resolve, reject) => {
      db.all(
        'SELECT * FROM videos WHERE processing_status IN (?, ?) ORDER BY created_at ASC',
        ['pending', 'processing'],
        (err, rows) => {
          if (err) {
            console.error('Error fetching processing queue:', err.message);
            return reject(err);
          }
          resolve(rows || []);
        }
      );
    });
  }

  static delete(id, userId = null) {
    return new Promise((resolve, reject) => {
      Video.findById(id)
        .then(video => {
          if (!video) {
            return resolve({ success: false, error: 'Video not found' });
          }

          // If userId is provided, verify ownership
          if (userId && video.user_id !== userId) {
            return resolve({ success: false, error: 'Not authorized to delete this video' });
          }

          // Store file paths for deletion after database operation
          const filesToDelete = [];
          if (video.filepath) {
            const fullPath = path.join(process.cwd(), 'public', video.filepath);
            filesToDelete.push({ path: fullPath, type: 'video' });
          }
          if (video.streaming_ready_path) {
            const streamingReadyPath = path.join(process.cwd(), 'public', video.streaming_ready_path);
            filesToDelete.push({ path: streamingReadyPath, type: 'streaming-ready' });
          }
          if (video.thumbnail_path) {
            const thumbnailPath = path.join(process.cwd(), 'public', video.thumbnail_path);
            filesToDelete.push({ path: thumbnailPath, type: 'thumbnail' });

            // Also check for WebP version (Google Drive imports create both JPG and WebP)
            const webpThumbnailPath = thumbnailPath.replace(/\.(jpg|jpeg)$/i, '.webp');
            if (webpThumbnailPath !== thumbnailPath) {
              filesToDelete.push({ path: webpThumbnailPath, type: 'thumbnail-webp' });
            }
          }

          // Delete from database first
          db.run('DELETE FROM videos WHERE id = ?', [id], function (err) {
            if (err) {
              console.error('❌ Error deleting video from database:', err.message);
              return resolve({ success: false, error: 'Database deletion failed: ' + err.message });
            }

            // Database deletion successful, now delete files
            const fileErrors = [];
            filesToDelete.forEach(file => {
              try {
                if (fs.existsSync(file.path)) {
                  fs.unlinkSync(file.path);
                  console.log(`✅ Deleted ${file.type} file: ${file.path}`);
                } else {
                  console.warn(`⚠️ ${file.type} file not found (already deleted?): ${file.path}`);
                }
              } catch (fileErr) {
                console.error(`❌ Error deleting ${file.type} file:`, fileErr.message);
                fileErrors.push(`Failed to delete ${file.type}: ${fileErr.message}`);
              }
            });

            // Return success even if some files couldn't be deleted (database is clean)
            if (fileErrors.length > 0) {
              console.warn(`⚠️ Video deleted from database but some files remain: ${fileErrors.join(', ')}`);
              return resolve({
                success: true,
                id,
                warnings: fileErrors,
                message: 'Video deleted but some files could not be removed'
              });
            }

            resolve({ success: true, id, message: 'Video and all files deleted successfully' });
          });
        })
        .catch(err => {
          console.error('❌ Error in Video.delete:', err);
          resolve({ success: false, error: 'Failed to delete video: ' + err.message });
        });
    });
  }

  // Utility method to verify file existence for a video
  static verifyFiles(id) {
    return new Promise((resolve, reject) => {
      Video.findById(id)
        .then(video => {
          if (!video) {
            return resolve({ exists: false, error: 'Video not found in database' });
          }

          const result = {
            video: {
              path: video.filepath,
              exists: false,
              fullPath: null
            },
            thumbnail: {
              path: video.thumbnail_path,
              exists: false,
              fullPath: null
            }
          };

          // Check video file
          if (video.filepath) {
            const fullPath = path.join(process.cwd(), 'public', video.filepath);
            result.video.fullPath = fullPath;
            result.video.exists = fs.existsSync(fullPath);
          }

          // Check thumbnail file
          if (video.thumbnail_path) {
            const thumbnailPath = path.join(process.cwd(), 'public', video.thumbnail_path);
            result.thumbnail.fullPath = thumbnailPath;
            result.thumbnail.exists = fs.existsSync(thumbnailPath);
          }

          resolve({ exists: true, video, files: result });
        })
        .catch(err => reject(err));
    });
  }
}
module.exports = Video;