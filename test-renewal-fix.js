#!/usr/bin/env node

/**
 * Test script to verify that the Transaction.updateMidtransData method works correctly
 * This script tests the renewal payment functionality that was failing
 */

const { db } = require('./db/database');
const Transaction = require('./models/Transaction');
const Subscription = require('./models/Subscription');

async function testRenewalFix() {
  console.log('🧪 Testing Renewal Fix (Transaction.updateMidtransData)...\n');

  try {
    // Step 1: Create a test transaction
    console.log('📝 Creating test transaction...');
    const testTransaction = await Transaction.create({
      user_id: 'test-user-id',
      plan_id: 'test-plan-id',
      order_id: `TEST-${Date.now()}`,
      amount_idr: 50000,
      payment_method: 'midtrans',
      status: 'pending'
    });

    console.log(`   ✅ Created transaction: ${testTransaction.id}`);
    console.log(`   Order ID: ${testTransaction.order_id}`);
    console.log(`   Initial Midtrans Token: ${testTransaction.midtrans_token || 'null'}`);
    console.log(`   Initial Redirect URL: ${testTransaction.midtrans_redirect_url || 'null'}\n`);

    // Step 2: Test updateMidtransData method
    console.log('🔄 Testing updateMidtransData method...');
    const testMidtransData = {
      midtrans_token: 'test-snap-token-12345',
      midtrans_redirect_url: 'https://app.sandbox.midtrans.com/snap/v2/vtweb/test-token'
    };

    const updateResult = await Transaction.updateMidtransData(testTransaction.id, testMidtransData);
    console.log(`   Update result: ${updateResult ? 'Success' : 'Failed'}`);

    // Step 3: Verify the update
    console.log('🔍 Verifying update...');
    const updatedTransaction = await Transaction.findByOrderId(testTransaction.order_id);
    
    if (updatedTransaction) {
      console.log(`   ✅ Transaction found after update`);
      console.log(`   Updated Midtrans Token: ${updatedTransaction.midtrans_token}`);
      console.log(`   Updated Redirect URL: ${updatedTransaction.midtrans_redirect_url}`);
      
      // Verify the values match
      const tokenMatches = updatedTransaction.midtrans_token === testMidtransData.midtrans_token;
      const urlMatches = updatedTransaction.midtrans_redirect_url === testMidtransData.midtrans_redirect_url;
      
      console.log(`   Token matches: ${tokenMatches ? '✅' : '❌'}`);
      console.log(`   URL matches: ${urlMatches ? '✅' : '❌'}`);
      
      if (tokenMatches && urlMatches) {
        console.log('\n🎉 updateMidtransData method is working correctly!');
      } else {
        console.log('\n❌ updateMidtransData method has issues');
      }
    } else {
      console.log('   ❌ Transaction not found after update');
    }

    // Step 4: Test with partial data
    console.log('\n🔄 Testing partial update (token only)...');
    const partialUpdateResult = await Transaction.updateMidtransData(testTransaction.id, {
      midtrans_token: 'updated-token-67890'
    });
    
    console.log(`   Partial update result: ${partialUpdateResult ? 'Success' : 'Failed'}`);
    
    const partiallyUpdatedTransaction = await Transaction.findByOrderId(testTransaction.order_id);
    if (partiallyUpdatedTransaction) {
      console.log(`   Token after partial update: ${partiallyUpdatedTransaction.midtrans_token}`);
      console.log(`   URL after partial update: ${partiallyUpdatedTransaction.midtrans_redirect_url}`);
      
      const partialTokenMatches = partiallyUpdatedTransaction.midtrans_token === 'updated-token-67890';
      const urlUnchanged = partiallyUpdatedTransaction.midtrans_redirect_url === testMidtransData.midtrans_redirect_url;
      
      console.log(`   Token updated correctly: ${partialTokenMatches ? '✅' : '❌'}`);
      console.log(`   URL unchanged: ${urlUnchanged ? '✅' : '❌'}`);
    }

    // Step 5: Clean up test transaction
    console.log('\n🧹 Cleaning up test transaction...');
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM transactions WHERE id = ?', [testTransaction.id], function(err) {
        if (err) reject(err);
        else {
          console.log(`   ✅ Deleted test transaction (${this.changes} rows affected)`);
          resolve();
        }
      });
    });

    console.log('\n✅ SUMMARY:');
    console.log('   - Transaction.updateMidtransData method has been implemented');
    console.log('   - Method can update both token and redirect URL');
    console.log('   - Method supports partial updates');
    console.log('   - Renewal payment functionality should now work correctly');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
if (require.main === module) {
  testRenewalFix().then(() => {
    console.log('\n🏁 Test completed');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
}

module.exports = { testRenewalFix };
