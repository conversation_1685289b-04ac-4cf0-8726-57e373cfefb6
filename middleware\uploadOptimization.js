const cacheService = require('../services/cacheService');
const Subscription = require('../models/Subscription');

class UploadOptimization {
  // Enable upload optimization mode
  static enableUploadMode() {
    return (req, res, next) => {
      cacheService.enableUploadMode();
      req.uploadOptimized = true;
      next();
    };
  }

  // Disable upload optimization mode
  static disableUploadMode() {
    return (req, res, next) => {
      if (req.uploadOptimized) {
        cacheService.disableUploadMode();
      }
      next();
    };
  }

  // Async storage update (non-blocking)
  static asyncStorageUpdate() {
    return (req, res, next) => {
      // Continue with response immediately
      next();
      
      // Update storage asynchronously
      if (req.uploadSizeGB && req.session.userId) {
        setImmediate(async () => {
          try {
            await Subscription.updateStorageUsage(req.session.userId, req.uploadSizeGB);
            console.log(`📊 Storage updated asynchronously: +${req.uploadSizeGB.toFixed(3)}GB for user ${req.session.userId}`);
          } catch (error) {
            console.error('❌ Async storage update error:', error);
          }
        });
      }
    };
  }

  // Batch storage updates for chunked uploads
  static batchStorageUpdate() {
    const pendingUpdates = new Map();
    const batchTimeout = 5000; // 5 seconds

    return (req, res, next) => {
      if (req.uploadSizeGB && req.session.userId) {
        const userId = req.session.userId;
        
        // Accumulate updates
        if (pendingUpdates.has(userId)) {
          pendingUpdates.get(userId).size += req.uploadSizeGB;
          clearTimeout(pendingUpdates.get(userId).timeout);
        } else {
          pendingUpdates.set(userId, { size: req.uploadSizeGB });
        }

        // Set new timeout for batch update
        pendingUpdates.get(userId).timeout = setTimeout(async () => {
          const update = pendingUpdates.get(userId);
          pendingUpdates.delete(userId);
          
          try {
            await Subscription.updateStorageUsage(userId, update.size);
            console.log(`📊 Batch storage updated: +${update.size.toFixed(3)}GB for user ${userId}`);
          } catch (error) {
            console.error('❌ Batch storage update error:', error);
          }
        }, batchTimeout);
      }
      
      next();
    };
  }
  
  // Skip storage updates for chunks, only update on finalize
  static skipChunkStorageUpdate() {
    return (req, res, next) => {
      // Skip storage update for chunk uploads
      req.skipStorageUpdate = true;
      next();
    };
  }
  
  // Optimized chunk processing
  static optimizeChunkProcessing() {
    return (req, res, next) => {
      // Set chunk-specific optimizations
      req.isChunkUpload = true;
      req.startTime = Date.now();
      
      // Disable unnecessary middleware
      if (process.env.BYPASS_CACHE_FOR_CHUNKS === 'true') {
        req.skipCache = true;
        req.skipQuotaCheck = true; // Check only on finalize
      }
      
      next();
    };
  }
}

module.exports = UploadOptimization;