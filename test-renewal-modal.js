#!/usr/bin/env node

/**
 * Test script to verify renewal payment functionality
 */

const { db } = require('./db/database');
const Subscription = require('./models/Subscription');
const midtransService = require('./services/midtrans');

async function testRenewalModal() {
  console.log('🧪 Testing Renewal Payment Modal Fix...\n');

  try {
    // Step 1: Find a user with an expired subscription
    console.log('🔍 Looking for users with expired subscriptions...');
    const expiredSub = await new Promise((resolve, reject) => {
      db.get(`
        SELECT us.*, u.username, sp.name as plan_name, sp.price
        FROM user_subscriptions us
        JOIN users u ON us.user_id = u.id
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE (us.status = 'expired' OR us.status = 'cancelled' OR us.end_date < datetime('now'))
        AND sp.name != 'Preview'
        AND sp.price > 0
        ORDER BY us.updated_at DESC
        LIMIT 1
      `, [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!expiredSub) {
      console.log('❌ No expired paid subscriptions found to test with');
      return;
    }

    console.log('📋 Found expired subscription:');
    console.log(`   User: ${expiredSub.username}`);
    console.log(`   Plan: ${expiredSub.plan_name} (${expiredSub.price} IDR)`);
    console.log(`   Status: ${expiredSub.status}\n`);

    // Step 2: Test Midtrans service
    console.log('🏦 Testing Midtrans service...');
    const testOrderId = `TEST-RENEWAL-${Date.now()}`;
    const testAmount = midtransService.validateIDRAmount(expiredSub.price);
    
    const testPaymentData = await midtransService.createTransaction({
      orderId: testOrderId,
      amount: testAmount,
      customerDetails: {
        first_name: expiredSub.username,
        email: `${expiredSub.username}@streamonpod.com`
      },
      itemDetails: [{
        id: expiredSub.plan_id,
        price: testAmount,
        quantity: 1,
        name: `${expiredSub.plan_name} Plan Renewal - 30 Days`
      }]
    });

    console.log(`   Midtrans Success: ${testPaymentData.success}`);
    if (testPaymentData.success) {
      console.log(`   ✅ Snap Token: ${testPaymentData.token ? 'Generated' : 'null'}`);
      console.log(`   ✅ Redirect URL: ${testPaymentData.redirect_url ? 'Available' : 'null'}`);
    } else {
      console.log(`   ❌ Error: ${testPaymentData.error}`);
    }

    console.log('\n✅ TEST RESULTS:');
    console.log(`   - Midtrans service: ${testPaymentData.success ? '✅ Working' : '❌ Failed'}`);
    console.log(`   - Snap token: ${testPaymentData.token ? '✅ Generated' : '❌ Missing'}`);
    
    if (testPaymentData.success && testPaymentData.token) {
      console.log('\n🎉 Renewal payment modal should work correctly!');
      console.log('   The fixes implemented should resolve the issue.');
    } else {
      console.log('\n⚠️ Issues detected with Midtrans service');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testRenewalModal().then(() => {
  console.log('\n🏁 Test completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test crashed:', error);
  process.exit(1);
});
