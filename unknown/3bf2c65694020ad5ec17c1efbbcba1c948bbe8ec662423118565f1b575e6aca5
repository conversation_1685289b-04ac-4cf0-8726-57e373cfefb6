#!/usr/bin/env node

/**
 * Fix Unlimited Subscriptions Script
 * Ensures only Preview plan has unlimited duration (null end_date)
 * All other plans must have proper expiration dates
 */

const { db } = require('../db/database');

class SubscriptionFixer {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.dryRun = process.argv.includes('--dry-run');
    this.stats = {
      checked: 0,
      fixed: 0,
      errors: 0,
      previewPlans: 0,
      validPaidPlans: 0
    };
  }

  async run() {
    try {
      console.log('🔍 Starting subscription duration fix...');
      console.log(`Mode: ${this.dryRun ? 'DRY RUN' : 'LIVE FIX'}`);
      console.log('');

      await this.checkAndFixSubscriptions();
      await this.printSummary();

    } catch (error) {
      console.error('❌ Fix failed:', error.message);
      process.exit(1);
    }
  }

  async checkAndFixSubscriptions() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT us.*, sp.name as plan_name, sp.price
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status = 'active'
        ORDER BY sp.name, us.created_at DESC
      `;

      db.all(query, [], async (err, subscriptions) => {
        if (err) {
          return reject(err);
        }

        console.log(`📊 Found ${subscriptions.length} active subscriptions to check`);
        console.log('');

        for (const subscription of subscriptions) {
          await this.processSubscription(subscription);
        }

        resolve();
      });
    });
  }

  async processSubscription(subscription) {
    this.stats.checked++;
    
    const { id, plan_name, price, end_date, user_id } = subscription;
    
    console.log(`Checking: ${plan_name} (User: ${user_id})`);

    // Preview plan should have null end_date
    if (plan_name === 'Preview') {
      this.stats.previewPlans++;
      
      if (end_date !== null) {
        console.log(`  ⚠️ Preview plan has end_date: ${end_date}`);
        
        if (!this.dryRun) {
          await this.fixPreviewPlan(id);
          console.log(`  ✅ Fixed: Set end_date to null for Preview plan`);
          this.stats.fixed++;
        } else {
          console.log(`  🔧 Would fix: Set end_date to null`);
        }
      } else {
        console.log(`  ✅ Preview plan correctly unlimited`);
      }
      return;
    }

    // Paid plans must have end_date
    if (price > 0 && !end_date) {
      console.log(`  ❌ Paid plan "${plan_name}" has no end_date!`);
      
      if (!this.dryRun) {
        await this.fixPaidPlan(id, plan_name);
        console.log(`  ✅ Fixed: Set 30-day expiration for paid plan`);
        this.stats.fixed++;
      } else {
        console.log(`  🔧 Would fix: Set 30-day expiration`);
      }
    } else if (price > 0 && end_date) {
      console.log(`  ✅ Paid plan has proper expiration: ${new Date(end_date).toLocaleDateString()}`);
      this.stats.validPaidPlans++;
    } else if (price === 0 && !end_date) {
      console.log(`  ⚠️ Free plan "${plan_name}" has no end_date (should this be Preview?)`);
    }
  }

  async fixPreviewPlan(subscriptionId) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE user_subscriptions SET end_date = NULL WHERE id = ?',
        [subscriptionId],
        function(err) {
          if (err) {
            console.error(`  ❌ Error fixing Preview plan: ${err.message}`);
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  async fixPaidPlan(subscriptionId, planName) {
    return new Promise((resolve, reject) => {
      // Set expiration to 30 days from now
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 30);
      
      db.run(
        'UPDATE user_subscriptions SET end_date = ? WHERE id = ?',
        [endDate.toISOString(), subscriptionId],
        function(err) {
          if (err) {
            console.error(`  ❌ Error fixing paid plan: ${err.message}`);
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  async printSummary() {
    console.log('');
    console.log('📋 Summary:');
    console.log('===========');
    console.log(`Total subscriptions checked: ${this.stats.checked}`);
    console.log(`Preview plans (unlimited): ${this.stats.previewPlans}`);
    console.log(`Valid paid plans: ${this.stats.validPaidPlans}`);
    console.log(`Subscriptions fixed: ${this.stats.fixed}`);
    console.log(`Errors: ${this.stats.errors}`);
    console.log('');

    if (this.dryRun) {
      console.log('🔧 This was a DRY RUN - no changes were made');
      console.log('Run without --dry-run to apply fixes');
    } else if (this.stats.fixed > 0) {
      console.log('✅ Fixes applied successfully!');
    } else {
      console.log('✅ No fixes needed - all subscriptions are properly configured');
    }
  }
}

// Run the fixer
const fixer = new SubscriptionFixer();
fixer.run().catch(console.error);
