-- Trigger untuk auto-update storage saat video ditambah
CREATE TRIGGER IF NOT EXISTS update_storage_on_video_insert
AFTER INSERT ON videos
FOR EACH ROW
BEGIN
  UPDATE users 
  SET used_storage_gb = (
    SELECT COALESCE(SUM(file_size), 0) / (1024 * 1024 * 1024)
    FROM videos 
    WHERE user_id = NEW.user_id
  ),
  updated_at = CURRENT_TIMESTAMP
  WHERE id = NEW.user_id;
END;

-- Trigger untuk auto-update storage saat video dihapus
CREATE TRIGGER IF NOT EXISTS update_storage_on_video_delete
AFTER DELETE ON videos
FOR EACH ROW
BEGIN
  UPDATE users 
  SET used_storage_gb = (
    SELECT COALESCE(SUM(file_size), 0) / (1024 * 1024 * 1024)
    FROM videos 
    WHERE user_id = OLD.user_id
  ),
  updated_at = CURRENT_TIMESTAMP
  WHERE id = OLD.user_id;
END;

-- Trigger untuk auto-update storage saat ukuran video berubah
CREATE TRIGGER IF NOT EXISTS update_storage_on_video_update
AFTER UPDATE OF file_size ON videos
FOR EACH ROW
WHEN OLD.file_size != NEW.file_size
BEGIN
  UPDATE users 
  SET used_storage_gb = (
    SELECT COALESCE(SUM(file_size), 0) / (1024 * 1024 * 1024)
    FROM videos 
    WHERE user_id = NEW.user_id
  ),
  updated_at = CURRENT_TIMESTAMP
  WHERE id = NEW.user_id;
END;