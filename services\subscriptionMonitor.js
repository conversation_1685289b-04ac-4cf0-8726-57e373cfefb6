/**
 * Subscription Monitor Service
 * Background service to monitor and handle expired subscriptions and slot limits
 */

const Subscription = require('../models/Subscription');
const notificationService = require('./notificationService');

class SubscriptionMonitor {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.checkInterval = 5 * 60 * 1000; // 5 minutes
    this.lastCheck = null;
    this.stats = {
      totalChecks: 0,
      expiredSubscriptionsProcessed: 0,
      streamsStoppedDueToExpiry: 0,
      streamsStoppedDueToSlotLimit: 0,
      lastProcessedExpiry: null,
      lastSlotEnforcement: null
    };
  }

  /**
   * Start the subscription monitoring service
   */
  start() {
    if (this.isRunning) {
      console.log('⚠️ Subscription monitor is already running');
      return;
    }

    console.log('🚀 Starting subscription monitor service...');
    this.isRunning = true;
    
    // Run initial check
    this.performCheck();
    
    // Schedule periodic checks
    this.intervalId = setInterval(() => {
      this.performCheck();
    }, this.checkInterval);

    console.log(`✅ Subscription monitor started (checking every ${this.checkInterval / 1000 / 60} minutes)`);
  }

  /**
   * Stop the subscription monitoring service
   */
  stop() {
    if (!this.isRunning) {
      console.log('⚠️ Subscription monitor is not running');
      return;
    }

    console.log('🛑 Stopping subscription monitor service...');
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    this.isRunning = false;
    console.log('✅ Subscription monitor stopped');
  }

  /**
   * Perform comprehensive subscription and slot checks
   */
  async performCheck() {
    try {
      console.log('🔍 [SubscriptionMonitor] Starting periodic check...');
      this.lastCheck = new Date();
      this.stats.totalChecks++;

      // 1. Check and handle expired subscriptions
      await this.checkExpiredSubscriptions();

      // 2. Enforce slot limits for all users
      await this.enforceSlotLimits();

      // 3. Check expired trials
      await this.checkExpiredTrials();

      console.log('✅ [SubscriptionMonitor] Periodic check completed');

    } catch (error) {
      console.error('❌ [SubscriptionMonitor] Error during periodic check:', error);
    }
  }

  /**
   * Check and handle expired subscriptions
   */
  async checkExpiredSubscriptions() {
    try {
      const result = await Subscription.checkAndHandleExpiredSubscriptions();
      
      if (result.processed > 0) {
        this.stats.expiredSubscriptionsProcessed += result.processed;
        this.stats.lastProcessedExpiry = new Date();
        
        console.log(`📉 [SubscriptionMonitor] Processed ${result.processed} expired subscriptions`);
        
        // Send notifications to affected users
        for (const user of result.expired) {
          try {
            await notificationService.notifySubscriptionExpired(
              user.userId,
              user.planName,
              user.expiredDate
            );

            // Also send renewal reminder notification
            await notificationService.notifyRenewalAvailable(
              user.userId,
              user.planName
            );
          } catch (notifError) {
            console.error('❌ Error sending expiry notification:', notifError);
          }
        }
      }

      return result;
    } catch (error) {
      console.error('❌ [SubscriptionMonitor] Error checking expired subscriptions:', error);
      return { processed: 0, expired: [] };
    }
  }

  /**
   * Enforce slot limits for all users
   */
  async enforceSlotLimits() {
    try {
      const result = await Subscription.enforceSlotLimits();
      
      if (result.stopped > 0) {
        this.stats.streamsStoppedDueToSlotLimit += result.stopped;
        this.stats.lastSlotEnforcement = new Date();
        
        console.log(`🛑 [SubscriptionMonitor] Stopped ${result.stopped} streams due to slot limits`);
        
        // Send notifications to affected users
        const userNotifications = new Map();
        for (const stream of result.streams) {
          if (!userNotifications.has(stream.userId)) {
            userNotifications.set(stream.userId, []);
          }
          userNotifications.get(stream.userId).push(stream);
        }
        
        for (const [userId, streams] of userNotifications) {
          try {
            await notificationService.notifyStreamsStoppedDueToSlotLimit(
              userId,
              streams.length,
              streams.map(s => s.title)
            );
          } catch (notifError) {
            console.error('❌ Error sending slot limit notification:', notifError);
          }
        }
      }

      return result;
    } catch (error) {
      console.error('❌ [SubscriptionMonitor] Error enforcing slot limits:', error);
      return { stopped: 0, streams: [] };
    }
  }

  /**
   * Check and handle expired trials
   */
  async checkExpiredTrials() {
    try {
      const result = await Subscription.checkExpiredTrials();
      
      if (result.length > 0) {
        console.log(`⏰ [SubscriptionMonitor] Processed ${result.length} expired trials`);
        
        // Send notifications to users whose trials expired
        for (const user of result) {
          try {
            await notificationService.notifyTrialExpired(
              user.id,
              user.trial_end_date
            );
          } catch (notifError) {
            console.error('❌ Error sending trial expiry notification:', notifError);
          }
        }
      }

      return result;
    } catch (error) {
      console.error('❌ [SubscriptionMonitor] Error checking expired trials:', error);
      return [];
    }
  }

  /**
   * Get monitoring statistics
   */
  getStats() {
    return {
      ...this.stats,
      isRunning: this.isRunning,
      lastCheck: this.lastCheck,
      checkInterval: this.checkInterval,
      nextCheck: this.isRunning && this.lastCheck ? 
        new Date(this.lastCheck.getTime() + this.checkInterval) : null
    };
  }

  /**
   * Force run a check manually
   */
  async forceCheck() {
    console.log('🔧 [SubscriptionMonitor] Manual check triggered');
    await this.performCheck();
    return this.getStats();
  }

  /**
   * Update check interval
   */
  setCheckInterval(minutes) {
    const newInterval = minutes * 60 * 1000;
    
    if (newInterval < 60000) { // Minimum 1 minute
      throw new Error('Check interval cannot be less than 1 minute');
    }
    
    this.checkInterval = newInterval;
    
    if (this.isRunning) {
      // Restart with new interval
      this.stop();
      this.start();
    }
    
    console.log(`⚙️ [SubscriptionMonitor] Check interval updated to ${minutes} minutes`);
  }

  /**
   * Check if a specific user's subscription is expired
   */
  async checkUserSubscription(userId) {
    try {
      const subscription = await Subscription.getUserSubscription(userId);
      
      if (!subscription) {
        return { expired: false, hasSubscription: false };
      }

      const isExpired = subscription.end_date && new Date(subscription.end_date) < new Date();
      
      if (isExpired) {
        console.log(`⚠️ [SubscriptionMonitor] User ${userId} subscription expired: ${subscription.end_date}`);
        await Subscription.handleExpiredSubscription(userId);
        return { expired: true, hasSubscription: true, expiredDate: subscription.end_date };
      }

      return { expired: false, hasSubscription: true, endDate: subscription.end_date };
    } catch (error) {
      console.error(`❌ [SubscriptionMonitor] Error checking user ${userId} subscription:`, error);
      return { expired: false, hasSubscription: false, error: error.message };
    }
  }

  /**
   * Emergency stop all streams for a specific user
   */
  async emergencyStopUserStreams(userId, reason = 'Emergency stop') {
    try {
      console.log(`🚨 [SubscriptionMonitor] Emergency stop for user ${userId}: ${reason}`);
      
      const streamingService = require('./streamingService');
      const Stream = require('../models/Stream');
      const activeStreamIds = streamingService.getActiveStreams();
      
      const stoppedStreams = [];
      
      for (const streamId of activeStreamIds) {
        try {
          const stream = await Stream.findById(streamId);
          if (stream && stream.user_id === userId) {
            await streamingService.stopStream(streamId);
            stoppedStreams.push({
              streamId: streamId,
              title: stream.title,
              reason: reason
            });
            console.log(`🛑 Emergency stopped stream ${streamId} for user ${userId}`);
          }
        } catch (error) {
          console.error(`❌ Error emergency stopping stream ${streamId}:`, error);
        }
      }

      return { stopped: stoppedStreams.length, streams: stoppedStreams };
    } catch (error) {
      console.error(`❌ [SubscriptionMonitor] Error in emergency stop for user ${userId}:`, error);
      return { stopped: 0, streams: [], error: error.message };
    }
  }
}

// Create singleton instance
const subscriptionMonitor = new SubscriptionMonitor();

module.exports = subscriptionMonitor;
