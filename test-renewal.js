/**
 * Test script for subscription renewal functionality
 * Run this script to test the renewal system
 */

const { db } = require('./db/database');
const Subscription = require('./models/Subscription');
const User = require('./models/User');

async function testRenewalFunctionality() {
  console.log('🧪 Testing Subscription Renewal Functionality...\n');

  try {
    // 1. Test getting renewal status for a user
    console.log('1. Testing renewal status check...');
    const testUserId = 'test-user-id'; // Replace with actual user ID
    
    const renewalStatus = await Subscription.canRenewSubscription(testUserId);
    console.log('   Renewal Status:', renewalStatus);

    // 2. Test getting all plans
    console.log('\n2. Testing plan retrieval...');
    const plans = await Subscription.getAllPlans();
    console.log('   Available Plans:', plans.map(p => ({ id: p.id, name: p.name, price: p.price })));

    // 3. Test getting user subscription including expired
    console.log('\n3. Testing expired subscription retrieval...');
    const userSub = await Subscription.getUserSubscriptionIncludingExpired(testUserId);
    console.log('   User Subscription:', userSub ? {
      plan_name: userSub.plan_name,
      end_date: userSub.end_date,
      isExpired: userSub.isExpired,
      status: userSub.status
    } : 'No subscription found');

    // 4. Test Preview plan behavior
    console.log('\n4. Testing Preview plan details...');
    const previewPlan = await Subscription.getPlanByName('Preview');
    console.log('   Preview Plan:', previewPlan ? {
      name: previewPlan.name,
      max_streaming_slots: previewPlan.max_streaming_slots,
      max_storage_gb: previewPlan.max_storage_gb,
      price: previewPlan.price
    } : 'Preview plan not found');

    console.log('\n✅ All tests completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('   - Renewal status check: ✅');
    console.log('   - Plan retrieval: ✅');
    console.log('   - Expired subscription handling: ✅');
    console.log('   - Preview plan verification: ✅');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function testExpiredSubscriptionHandling() {
  console.log('\n🔄 Testing Expired Subscription Handling...\n');

  try {
    // Test the expired subscription check
    const result = await Subscription.checkAndHandleExpiredSubscriptions();
    console.log('Expired subscription check result:', result);

    console.log('\n✅ Expired subscription handling test completed!');
  } catch (error) {
    console.error('❌ Expired subscription test failed:', error);
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Subscription Renewal Tests...\n');
  
  await testRenewalFunctionality();
  await testExpiredSubscriptionHandling();
  
  console.log('\n🎉 All tests completed!');
  console.log('\n📝 Next Steps:');
  console.log('   1. Test the renewal API endpoints via HTTP requests');
  console.log('   2. Test the frontend renewal modal');
  console.log('   3. Verify expired subscription behavior in the UI');
  console.log('   4. Test notification system for renewals');
  
  process.exit(0);
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testRenewalFunctionality,
  testExpiredSubscriptionHandling,
  runTests
};
