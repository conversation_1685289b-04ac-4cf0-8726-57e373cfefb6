const express = require('express');
const router = express.Router();
const Subscription = require('../models/Subscription');
const User = require('../models/User');
const QuotaMiddleware = require('../middleware/quotaMiddleware');
const notificationService = require('../services/notificationService');

// Middleware to check authentication
const isAuthenticated = (req, res, next) => {
  console.log('[AUTH CHECK] Session data:', {
    sessionId: req.sessionID,
    userId: req.session?.userId,
    hasSession: !!req.session,
    sessionKeys: req.session ? Object.keys(req.session) : [],
    userAgent: req.get('User-Agent'),
    host: req.get('Host'),
    origin: req.get('Origin')
  });

  if (req.session && req.session.userId) {
    console.log('[AUTH CHECK] ✅ User authenticated:', req.session.userId);
    return next();
  }

  console.log('[AUTH CHECK] ❌ User not authenticated');

  // Check if this is an API request (JSON expected)
  if (req.path.startsWith('/api/') || req.xhr || req.get('Accept')?.includes('application/json')) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required',
      redirect: '/login'
    });
  }

  // For regular page requests, redirect to login
  res.redirect('/login');
};

// Get all subscription plans
router.get('/plans', async (req, res) => {
  try {
    const plans = await Subscription.getAllPlans();

    // If user is logged in, get their current subscription
    let currentSubscription = null;
    let quotaInfo = null;
    let trialInfo = null;

    if (req.session.userId) {
      // Check and handle any expired subscriptions first
      await Subscription.checkAndHandleExpiredSubscriptions();

      currentSubscription = await Subscription.getUserSubscription(req.session.userId);

      // Check for active trial
      const User = require('../models/User');
      trialInfo = await User.hasActiveTrial(req.session.userId);

      // Check for expired subscriptions first (but only if they have valid end_date)
      if (!currentSubscription) {
        // Get the most recent subscription (including expired ones)
        const recentSubscription = await Subscription.getUserSubscriptionIncludingExpired(req.session.userId);
        if (recentSubscription && recentSubscription.end_date) {
          // Only show expired subscription if it has a valid end_date
          currentSubscription = recentSubscription;
        }
      }

      // If still no subscription found, check if user is on Preview plan
      if (!currentSubscription) {
        const user = await User.findById(req.session.userId);
        if (user && user.plan_type) {
          // Create a subscription-like object for Preview plan display
          currentSubscription = {
            plan_name: user.plan_type,
            max_streaming_slots: user.max_streaming_slots,
            max_storage_gb: user.max_storage_gb,
            status: 'active',
            isUserPlan: true, // Flag to indicate this comes from users table
            // For Preview plan, no end_date (unlimited)
            end_date: null
          };
        }
      }

      quotaInfo = await QuotaMiddleware.getUserQuotaInfo(req.session.userId);
    }

    // Debug logging to track subscription data
    if (currentSubscription) {
      console.log(`🔍 [DEBUG] Subscription data for user ${req.session.userId}:`);
      console.log(`   Plan: ${currentSubscription.plan_name}`);
      console.log(`   End Date: ${currentSubscription.end_date}`);
      console.log(`   Is User Plan: ${currentSubscription.isUserPlan}`);
      console.log(`   Status: ${currentSubscription.status}`);
    }

    // Check if user has expired subscription (enables downgrade purchases)
    const expiredSubscriptionInfo = await Subscription.hasExpiredSubscription(req.session.userId);

    res.render('subscription/plans', {
      title: 'Subscription Plans',
      active: 'subscription',
      plans,
      currentSubscription,
      quotaInfo,
      trialInfo,
      expiredSubscriptionInfo,
      midtransClientKey: process.env.MIDTRANS_CLIENT_KEY
    });
  } catch (error) {
    console.error('Get plans error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load subscription plans',
      error: error
    });
  }
});

// Get user's current subscription info
router.get('/current', isAuthenticated, async (req, res) => {
  try {
    let subscription = await Subscription.getUserSubscription(req.session.userId);

    // Check for active trial
    const User = require('../models/User');
    const trialInfo = await User.hasActiveTrial(req.session.userId);

    // If no active subscription found, check if user is on Preview plan
    if (!subscription) {
      const user = await User.findById(req.session.userId);
      if (user && user.plan_type) {
        // Create a subscription-like object for Preview plan display
        subscription = {
          plan_name: user.plan_type,
          max_streaming_slots: user.max_streaming_slots,
          max_storage_gb: user.max_storage_gb,
          status: 'active',
          isUserPlan: true // Flag to indicate this comes from users table
        };
      }
    }

    const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(req.session.userId);

    res.json({
      subscription,
      quota: quotaInfo,
      trial: trialInfo
    });
  } catch (error) {
    console.error('Get current subscription error:', error);
    res.status(500).json({ error: 'Failed to get subscription information' });
  }
});

// Subscribe to a plan
router.post('/subscribe', isAuthenticated, async (req, res) => {
  try {
    console.log('📝 Subscribe request received:', req.body);
    const { planId, paymentMethod = 'midtrans' } = req.body;

    if (!planId) {
      console.log('❌ No plan ID provided');
      return res.status(400).json({ error: 'Plan ID is required' });
    }

    console.log('🔍 Looking for plan:', planId);
    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      console.log('❌ Plan not found:', planId);
      return res.status(404).json({ error: 'Plan not found' });
    }

    console.log('✅ Plan found:', plan.name, 'Price:', plan.price);

    // Get user's current plan
    console.log('🔍 Looking for user:', req.session.userId);
    const user = await User.findById(req.session.userId);
    if (!user) {
      console.log('❌ User not found:', req.session.userId);
      return res.status(404).json({ error: 'User not found' });
    }

    console.log('✅ User found:', user.username);

    // Check if user has expired subscription (allows downgrades)
    const expiredInfo = await Subscription.hasExpiredSubscription(req.session.userId);

    // Get user's current plan details for comparison
    const currentPlan = await Subscription.getPlanByName(user.plan_type);
    if (currentPlan) {
      console.log('📋 Current plan:', currentPlan.name, 'Price:', currentPlan.price);
      console.log('📋 Has expired subscription:', expiredInfo.hasExpired);

      // Allow downgrades only for users with expired subscriptions
      if (plan.price > 0 && currentPlan.price > 0 && plan.price < currentPlan.price) {
        if (!expiredInfo.hasExpired) {
          console.log('❌ Downgrade to paid plan not allowed for active subscriptions');
          return res.status(400).json({
            error: 'Tidak dapat downgrade ke plan berbayar yang lebih rendah. Silakan hubungi admin jika ingin mengubah plan.',
            current_plan: currentPlan.name,
            current_price: currentPlan.price,
            target_plan: plan.name,
            target_price: plan.price
          });
        } else {
          console.log('✅ Downgrade allowed for expired subscription');
        }
      }

      // Prevent subscribing to same plan (unless it's a renewal after expiration)
      if (currentPlan.id === planId && !expiredInfo.hasExpired) {
        console.log('❌ User already on this plan');
        return res.status(400).json({
          error: 'Anda sudah menggunakan plan ini.',
          current_plan: currentPlan.name
        });
      }
    }

    // For paid plans, redirect to payment creation
    if (plan.price > 0) {
      console.log('💰 Paid plan detected, requiring payment');
      console.log('💰 Plan price:', plan.price, 'Currency:', plan.currency);

      // For expired users, use full pricing (no prorated calculations)
      if (expiredInfo.hasExpired) {
        console.log('💰 Expired user - using full plan pricing');
        const midtransService = require('../services/midtrans');

        return res.json({
          success: true,
          requires_payment: true,
          plan_id: planId,
          plan_name: plan.name,
          price_idr: plan.price,
          message: 'Payment required for this plan',
          expired_user: true,
          full_price: true,
          previous_plan: expiredInfo.previousPlan,
          price_formatted: midtransService.formatIDR(plan.price)
        });
      }

      // Calculate prorated upgrade pricing for active subscription holders
      try {
        const proratedCalculation = await Subscription.calculateProratedUpgrade(req.session.userId, planId);
        const midtransService = require('../services/midtrans');

        return res.json({
          success: true,
          requires_payment: true,
          plan_id: planId,
          plan_name: plan.name,
          price_idr: plan.price, // Display original plan price (without admin fee)
          message: 'Payment required for this plan',
          prorated_info: {
            isUpgrade: proratedCalculation.isUpgrade,
            currentPlan: proratedCalculation.currentPlan?.name || null,
            remainingDays: proratedCalculation.remainingDays,
            savings: proratedCalculation.savings,
            upgradePrice: proratedCalculation.upgradePrice,
            totalPayment: proratedCalculation.totalPayment,
            // Formatted for display
            savingsFormatted: midtransService.formatIDR(proratedCalculation.savings),
            upgradePriceFormatted: midtransService.formatIDR(proratedCalculation.upgradePrice),
            totalPaymentFormatted: midtransService.formatIDR(proratedCalculation.totalPayment)
          }
        });
      } catch (error) {
        console.error('Error calculating prorated upgrade:', error);
        return res.json({
          success: true,
          requires_payment: true,
          plan_id: planId,
          plan_name: plan.name,
          price_idr: plan.price,
          message: 'Payment required for this plan'
        });
      }
    }

    // Check if user already has an active subscription (excluding Preview plan)
    const existingSubscription = await Subscription.getUserSubscription(req.session.userId);
    if (existingSubscription) {
      return res.status(400).json({
        error: 'You already have an active subscription',
        message: 'Please cancel your current subscription before subscribing to a new plan'
      });
    }

    // Allow upgrade from Preview plan to any other plan
    if (user.plan_type === 'Preview' && plan.name === 'Preview') {
      return res.status(400).json({
        error: 'You are already on the Preview plan',
        message: 'You are already using the Preview plan'
      });
    }

    // Calculate end date (30 days from now for monthly plans)
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 30);

    // Create subscription
    const subscription = await Subscription.createSubscription({
      user_id: req.session.userId,
      plan_id: planId,
      status: 'active',
      end_date: endDate.toISOString(),
      payment_method: paymentMethod
    });

    // Update user's plan limits
    await User.updatePlan(
      req.session.userId,
      plan.name,
      plan.max_streaming_slots,
      plan.max_storage_gb
    );

    console.log(`📈 User ${user.username} upgraded from ${user.plan_type} to ${plan.name} plan`);

    // Send notification for plan upgrade (admin notification)
    try {
      await notificationService.notifyUserUpgraded(req.session.userId, user.username, user.plan_type, plan.name);
    } catch (notifError) {
      console.error('Error sending plan upgrade notification:', notifError);
    }

    // Send notification to the user about their plan change
    try {
      await notificationService.notifyUserPlanChanged(req.session.userId, user.plan_type, plan.name, true);
    } catch (notifError) {
      console.error('Error sending user plan change notification:', notifError);
    }

    res.json({
      success: true,
      message: 'Successfully subscribed to plan',
      subscription,
      plan
    });
  } catch (error) {
    console.error('❌ Subscribe error:', error);
    console.error('Error details:', error.message);
    console.error('Stack trace:', error.stack);
    res.status(500).json({ error: 'Failed to create subscription' });
  }
});



// Upgrade/Downgrade subscription
router.post('/change', isAuthenticated, async (req, res) => {
  try {
    const { planId } = req.body;

    if (!planId) {
      return res.status(400).json({ error: 'Plan ID is required' });
    }

    const newPlan = await Subscription.getPlanById(planId);
    if (!newPlan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    const currentSubscription = await Subscription.getUserSubscription(req.session.userId);

    if (currentSubscription) {
      // Cancel current subscription
      await Subscription.updateSubscriptionStatus(currentSubscription.id, 'cancelled');
    }

    // Create new subscription
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 30);

    const newSubscription = await Subscription.createSubscription({
      user_id: req.session.userId,
      plan_id: planId,
      status: 'active',
      end_date: endDate.toISOString(),
      payment_method: 'manual'
    });

    // Update user's plan limits
    await User.updatePlan(
      req.session.userId,
      newPlan.name,
      newPlan.max_streaming_slots,
      newPlan.max_storage_gb
    );

    res.json({
      success: true,
      message: 'Plan changed successfully',
      subscription: newSubscription,
      plan: newPlan
    });
  } catch (error) {
    console.error('Change plan error:', error);
    res.status(500).json({ error: 'Failed to change subscription plan' });
  }
});

// Check renewal eligibility
router.get('/renewal-status', isAuthenticated, async (req, res) => {
  try {
    const renewalStatus = await Subscription.canRenewSubscription(req.session.userId);

    res.json({
      success: true,
      ...renewalStatus
    });
  } catch (error) {
    console.error('Renewal status check error:', error);
    res.status(500).json({ error: 'Failed to check renewal status' });
  }
});

// Renew expired subscription
router.post('/renew', isAuthenticated, async (req, res) => {
  try {
    const { planId, durationDays = 30 } = req.body;

    if (!planId) {
      return res.status(400).json({ error: 'Plan ID is required' });
    }

    // Check if user can renew
    const renewalStatus = await Subscription.canRenewSubscription(req.session.userId);
    if (!renewalStatus.canRenew) {
      return res.status(400).json({
        error: 'Cannot renew subscription',
        message: 'You already have an active subscription or no expired subscription found'
      });
    }

    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    // All paid plans require payment - no free renewals
    if (plan.price > 0) {
      return res.status(400).json({
        error: 'Payment required',
        message: 'Renewal requires payment. Please use the payment system to renew your subscription.',
        requiresPayment: true,
        plan: plan,
        renewalFlow: true
      });
    }

    // Only Preview plan (price = 0) can be renewed without payment
    if (plan.name !== 'Preview') {
      return res.status(400).json({
        error: 'Invalid renewal',
        message: 'Only Preview plan can be renewed without payment. Paid plans require payment processing.',
        requiresPayment: true,
        plan: plan
      });
    }

    // Renew the subscription (only for Preview plan)
    const newSubscription = await Subscription.renewSubscription(
      req.session.userId,
      planId,
      durationDays
    );

    console.log(`🔄 User ${req.session.userId} renewed subscription to ${plan.name} plan`);

    res.json({
      success: true,
      message: 'Subscription renewed successfully',
      subscription: newSubscription,
      plan: plan
    });

  } catch (error) {
    console.error('Renewal error:', error);
    res.status(500).json({
      error: 'Failed to renew subscription',
      message: error.message
    });
  }
});

// Extend existing subscription
router.post('/extend', isAuthenticated, async (req, res) => {
  try {
    const { additionalDays = 30, paymentMethod = 'manual' } = req.body;

    // Get current subscription
    const currentSubscription = await Subscription.getUserSubscription(req.session.userId);
    if (!currentSubscription) {
      return res.status(400).json({
        error: 'No active subscription',
        message: 'You need an active subscription to extend it'
      });
    }

    // Extend the subscription
    const extendedSubscription = await Subscription.extendSubscription(
      req.session.userId,
      additionalDays
    );

    console.log(`⏰ User ${req.session.userId} extended subscription by ${additionalDays} days`);

    res.json({
      success: true,
      message: `Subscription extended by ${additionalDays} days`,
      subscription: extendedSubscription
    });

  } catch (error) {
    console.error('Extension error:', error);
    res.status(500).json({
      error: 'Failed to extend subscription',
      message: error.message
    });
  }
});

// Get subscription history
router.get('/history', isAuthenticated, async (req, res) => {
  try {
    const { db } = require('../db/database');

    const history = await new Promise((resolve, reject) => {
      db.all(`
        SELECT us.*, sp.name as plan_name, sp.price, sp.currency
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ?
        ORDER BY us.created_at DESC
      `, [req.session.userId], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    res.json(history);
  } catch (error) {
    console.error('Get subscription history error:', error);
    res.status(500).json({ error: 'Failed to get subscription history' });
  }
});

// Check quota status
router.get('/quota', isAuthenticated, async (req, res) => {
  try {
    const isProduction = process.env.NODE_ENV === 'production';
    const enableConsoleLogging = !isProduction; // ✅ Disable di production

    // Enhanced logging untuk debugging tunnel vs local
    console.log('[QUOTA API] Request received:', {
      userId: req.session.userId,
      sessionId: req.sessionID,
      userAgent: req.get('User-Agent'),
      host: req.get('Host'),
      origin: req.get('Origin'),
      referer: req.get('Referer'),
      protocol: req.protocol,
      secure: req.secure,
      ip: req.ip
    });

    if (!isProduction && enableConsoleLogging) {
      console.log('🔍 Quota API called for user:', req.session.userId);
    }
    const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(req.session.userId);
    if (!isProduction && enableConsoleLogging) {
      console.log('📊 QuotaMiddleware result:', quotaInfo);
    }

    const response = {
      success: true,
      streaming: quotaInfo.streaming,
      storage: quotaInfo.storage,
      subscription: quotaInfo.subscription,
      plan: quotaInfo.plan
    };

    console.log('[QUOTA API] Sending response:', {
      success: response.success,
      hasStreaming: !!response.streaming,
      hasStorage: !!response.storage,
      hasSubscription: !!response.subscription,
      hasPlan: !!response.plan
    });

    if (!isProduction && enableConsoleLogging) {
      console.log('📤 Sending quota response:', response);
    }
    res.json(response);
  } catch (error) {
    console.error('❌ Get quota error:', error);
    console.error('❌ Error details:', {
      message: error.message,
      stack: error.stack,
      userId: req.session?.userId
    });
    res.status(500).json({ success: false, error: 'Failed to get quota information' });
  }
});

// Get plans that support advanced settings
router.get('/advanced-eligible-plans', isAuthenticated, async (req, res) => {
  try {
    const QuotaMiddleware = require('../middleware/quotaMiddleware');
    const eligiblePlans = await QuotaMiddleware.getAdvancedSettingsEligiblePlans();

    res.json({
      success: true,
      plans: eligiblePlans,
      minimumPrice: 49900
    });
  } catch (error) {
    console.error('❌ Error getting eligible plans:', error);
    res.status(500).json({ success: false, error: 'Failed to get eligible plans' });
  }
});

// Create renewal payment (integrates with payment system)
router.post('/renew-payment', isAuthenticated, async (req, res) => {
  try {
    const { planId } = req.body;

    if (!planId) {
      return res.status(400).json({ error: 'Plan ID is required' });
    }

    // Check if user can renew
    const renewalStatus = await Subscription.canRenewSubscription(req.session.userId);
    if (!renewalStatus.canRenew) {
      return res.status(400).json({
        error: 'Cannot renew subscription',
        message: 'You already have an active subscription or no expired subscription found'
      });
    }

    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    // Only allow renewal for paid plans
    if (plan.price <= 0) {
      return res.status(400).json({
        error: 'Invalid renewal plan',
        message: 'Free plans do not require payment renewal'
      });
    }

    // Get user details
    const User = require('../models/User');
    const user = await User.findById(req.session.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Calculate renewal pricing (full price for renewal)
    const midtransService = require('../services/midtrans');
    const amountIDR = midtransService.validateIDRAmount(plan.price);

    // Generate unique order ID for renewal
    const Transaction = require('../models/Transaction');
    const orderId = Transaction.generateOrderId(req.session.userId, planId, 'renewal');

    // Create transaction record
    const transaction = await Transaction.create({
      user_id: req.session.userId,
      plan_id: planId,
      order_id: orderId,
      amount_idr: amountIDR,
      payment_method: 'midtrans',
      status: 'pending'
    });

    // Create Midtrans payment
    const paymentData = await midtransService.createTransaction({
      orderId: orderId,
      amount: amountIDR,
      customerDetails: {
        first_name: user.username,
        email: user.email || `${user.username}@streamonpod.com`
      },
      itemDetails: [{
        id: planId,
        price: amountIDR,
        quantity: 1,
        name: `${plan.name} Plan Renewal - 30 Days`
      }]
    });

    // Check if Midtrans payment creation was successful
    if (!paymentData.success) {
      console.error('❌ Midtrans payment creation failed:', paymentData.error);
      return res.status(500).json({
        error: 'Failed to create payment',
        message: paymentData.error || 'Payment gateway error'
      });
    }

    // Update transaction with Midtrans data
    await Transaction.updateMidtransData(transaction.id, {
      midtrans_token: paymentData.token,
      midtrans_redirect_url: paymentData.redirect_url
    });

    console.log(`💳 Created renewal payment for user ${user.username}: ${plan.name} plan - ${amountIDR} IDR`);
    console.log(`🎫 Snap token: ${paymentData.token ? paymentData.token.substring(0, 20) + '...' : 'null'}`);

    res.json({
      success: true,
      message: 'Renewal payment created successfully',
      payment: {
        snap_token: paymentData.token,
        redirect_url: paymentData.redirect_url,
        order_id: orderId,
        amount: amountIDR,
        plan: plan,
        renewal: true
      }
    });

  } catch (error) {
    console.error('Renewal payment error:', error);
    res.status(500).json({
      error: 'Failed to create renewal payment',
      message: error.message
    });
  }
});

// Force refresh user quota (for debugging expired subscriptions)
router.post('/refresh-quota', isAuthenticated, async (req, res) => {
  try {
    console.log(`🔄 Force refreshing quota for user ${req.session.userId}`);

    // Check and handle any expired subscriptions first
    await Subscription.checkAndHandleExpiredSubscriptions();

    // Get fresh quota info
    const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(req.session.userId);

    console.log(`📊 Refreshed quota info:`, {
      streaming: quotaInfo.streaming,
      plan: quotaInfo.plan.name
    });

    res.json({
      success: true,
      message: 'Quota refreshed successfully',
      quota: quotaInfo
    });
  } catch (error) {
    console.error('Refresh quota error:', error);
    res.status(500).json({ error: 'Failed to refresh quota' });
  }
});

// Demo route for prorated upgrade system
router.get('/upgrade-demo', (req, res) => {
  res.render('subscription/upgrade-demo', {
    title: 'Demo Prorated Upgrade System',
    active: 'subscription'
  });
});

// Preview a trial plan without activating it
router.post('/preview-trial', isAuthenticated, async (req, res) => {
  try {
    const { planId } = req.body;

    if (!planId) {
      return res.status(400).json({ success: false, error: 'Plan ID is required' });
    }

    const plan = await Subscription.getPlanById(planId);

    if (!plan) {
      return res.status(404).json({ success: false, error: 'Plan not found' });
    }

    // Check if the plan has a trial period
    if (plan.trial_days && plan.trial_days > 0) {
      res.json({
        success: true,
        trial_details: {
          plan_name: plan.name,
          trial_days: plan.trial_days,
          max_streaming_slots: plan.max_streaming_slots,
          max_storage_gb: plan.max_storage_gb,
          is_trial_preview: true
        }
      });
    } else {
      res.status(400).json({ success: false, error: 'This plan does not offer a trial' });
    }
  } catch (error) {
    console.error('Trial preview error:', error);
    res.status(500).json({ success: false, error: 'Failed to retrieve trial information' });
  }
});
module.exports = router;
