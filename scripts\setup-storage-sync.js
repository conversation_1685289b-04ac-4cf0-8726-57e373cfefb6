const { db } = require('../db/database');
const fs = require('fs');
const path = require('path');

async function setupStorageSync() {
  try {
    console.log('🔧 Setting up storage synchronization system...');
    
    // Read and execute the SQL migration
    const migrationPath = path.join(__dirname, '../db/migrations/add_storage_sync_triggers.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Remove comments and split by END; pattern for triggers
    const cleanSQL = migrationSQL
      .split('\n')
      .filter(line => !line.trim().startsWith('--'))
      .join('\n');
    
    // Split by 'END;' to separate trigger statements
    const statements = cleanSQL
      .split(/END;/i)
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0)
      .map(stmt => stmt + (stmt.toUpperCase().includes('CREATE TRIGGER') ? ' END;' : ''));
    
    for (const statement of statements) {
      if (statement.trim()) {
        await new Promise((resolve, reject) => {
          db.run(statement, (err) => {
            if (err) {
              console.error('❌ Error executing statement:', err);
              console.error('Statement was:', statement);
              reject(err);
            } else {
              console.log('✅ Executed statement successfully');
              resolve();
            }
          });
        });
      }
    }
    
    console.log('✅ Database triggers created successfully');
    
    // Perform initial sync for all users
    const QuotaMiddleware = require('../middleware/quotaMiddleware');
    const syncedUsers = await QuotaMiddleware.syncAllUsersStorageCache();
    
    console.log(`✅ Storage sync setup completed! Synced ${syncedUsers} users.`);
    
  } catch (error) {
    console.error('❌ Error setting up storage sync:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  setupStorageSync().then(() => {
    console.log('🎉 Storage synchronization system is now active!');
    process.exit(0);
  });
}

module.exports = { setupStorageSync };