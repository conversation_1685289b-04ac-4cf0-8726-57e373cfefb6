<% layout('layout') -%>

<div class="min-h-screen bg-dark-900 text-white">
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
      <div>
        <h1 class="text-3xl font-bold mb-2"><%= t('admin.admin_notifications') %></h1>
        <p class="text-gray-400"><%= t('admin.admin_notifications_subtitle') %></p>
      </div>
      <div class="flex items-center space-x-4 mt-4 md:mt-0">
        <button id="markAllReadBtn" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
          <i class="ti ti-check-all mr-2"></i><%= t('admin.mark_all_read') %>
        </button>
        <button id="cleanupBtn" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg transition-colors">
          <i class="ti ti-trash mr-2"></i><%= t('admin.cleanup_old') %>
        </button>
        <button id="testNotificationBtn" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors">
          <i class="ti ti-bell-plus mr-2"></i><%= t('admin.test_notification') %>
        </button>
        <button id="createSamplesBtn" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg transition-colors">
          <i class="ti ti-database-plus mr-2"></i><%= t('admin.create_samples') %>
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm"><%= t('admin.total_notifications') %></p>
            <p class="text-2xl font-bold text-white" id="totalCount">0</p>
          </div>
          <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-bell text-blue-400 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm"><%= t('admin.unread') %></p>
            <p class="text-2xl font-bold text-orange-400" id="unreadCount">0</p>
          </div>
          <div class="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-bell-ringing text-orange-400 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm"><%= t('admin.critical') %></p>
            <p class="text-2xl font-bold text-red-400" id="criticalCount">0</p>
          </div>
          <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-alert-triangle text-red-400 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm"><%= t('admin.system_alerts') %></p>
            <p class="text-2xl font-bold text-yellow-400" id="systemCount">0</p>
          </div>
          <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-server text-yellow-400 text-xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-dark-800 rounded-lg p-6 border border-gray-700 mb-8">
      <h3 class="text-lg font-semibold mb-4"><%= t('admin.filters') %></h3>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.type') %></label>
          <select id="typeFilter" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value=""><%= t('admin.all_types') %></option>
            <option value="info"><%= t('admin.info') %></option>
            <option value="warning"><%= t('admin.warning') %></option>
            <option value="error"><%= t('admin.error') %></option>
            <option value="success"><%= t('admin.success') %></option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.category') %></label>
          <select id="categoryFilter" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value=""><%= t('admin.all_categories') %></option>
            <option value="system"><%= t('admin.system') %></option>
            <option value="users"><%= t('admin.users') %></option>
            <option value="streams"><%= t('admin.streams') %></option>
            <option value="performance"><%= t('admin.performance') %></option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.priority') %></label>
          <select id="priorityFilter" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value=""><%= t('admin.all_priorities') %></option>
            <option value="low"><%= t('admin.low') %></option>
            <option value="normal"><%= t('admin.normal') %></option>
            <option value="high"><%= t('admin.high') %></option>
            <option value="critical"><%= t('admin.critical') %></option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.status') %></label>
          <select id="statusFilter" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value=""><%= t('admin.all') %></option>
            <option value="false"><%= t('admin.unread') %></option>
            <option value="true"><%= t('admin.read') %></option>
          </select>
        </div>
      </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-dark-800 rounded-lg border border-gray-700">
      <div class="p-6 border-b border-gray-700">
        <h3 class="text-lg font-semibold"><%= t('admin.notifications') %></h3>
      </div>
      <div id="notificationsList" class="divide-y divide-gray-700">
        <!-- Notifications will be loaded here -->
      </div>
      <div id="loadingIndicator" class="p-8 text-center text-gray-400">
        <i class="ti ti-loader-2 animate-spin text-2xl mb-2"></i>
        <p><%= t('admin.loading_notifications') %></p>
      </div>
      <div id="emptyState" class="p-8 text-center text-gray-400 hidden">
        <i class="ti ti-bell-off text-4xl mb-4"></i>
        <p><%= t('admin.no_notifications_found') %></p>
      </div>
      <div id="debugStatus" class="p-4 bg-blue-900 text-blue-200 text-sm hidden">
        <strong>Debug Status:</strong> <span id="debugMessage">Initializing...</span>
      </div>
    </div>

    <!-- Pagination -->
    <div id="pagination" class="flex justify-center mt-8 hidden">
      <div class="flex items-center space-x-2">
        <button id="prevPage" class="px-4 py-2 bg-dark-700 border border-gray-600 rounded-lg hover:bg-dark-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
          <i class="ti ti-chevron-left"></i>
        </button>
        <span id="pageInfo" class="px-4 py-2 text-gray-300">Page 1</span>
        <button id="nextPage" class="px-4 py-2 bg-dark-700 border border-gray-600 rounded-lg hover:bg-dark-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
          <i class="ti ti-chevron-right"></i>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Test Notification Modal -->
<div id="testNotificationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-dark-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
      <h3 class="text-lg font-semibold mb-4"><%= t('admin.create_test_notification') %></h3>
      <form id="testNotificationForm">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.title') %></label>
          <input type="text" id="testTitle" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white" value="<%= t('admin.test_notification_title') %>">
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.message') %></label>
          <textarea id="testMessage" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white h-20"><%= t('admin.test_notification_message') %></textarea>
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.type') %></label>
          <select id="testType" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value="info"><%= t('admin.info') %></option>
            <option value="warning"><%= t('admin.warning') %></option>
            <option value="error"><%= t('admin.error') %></option>
            <option value="success"><%= t('admin.success') %></option>
          </select>
        </div>
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.priority') %></label>
          <select id="testPriority" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value="low"><%= t('admin.low') %></option>
            <option value="normal"><%= t('admin.normal') %></option>
            <option value="high"><%= t('admin.high') %></option>
            <option value="critical"><%= t('admin.critical') %></option>
          </select>
        </div>
        <div class="flex justify-end space-x-3">
          <button type="button" id="cancelTestBtn" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"><%= t('common.cancel') %></button>
          <button type="submit" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"><%= t('admin.create') %></button>
        </div>
      </form>
    </div>
  </div>
</div>

<script src="/socket.io/socket.io.js"></script>
<script>
  // Translation variables for JavaScript
  const translations = {
    newNotificationReceived: '<%= t("notifications.new_notification_received") %>',
    markAllReadConfirm: '<%= t("admin.mark_all_read_confirm") %>',
    cleanupOldConfirm: '<%= t("admin.cleanup_old_confirm") %>',
    createSamplesConfirm: '<%= t("admin.create_samples_confirm") %>',
    deleteNotificationConfirm: '<%= t("admin.delete_notification_confirm") %>',
    markAsRead: '<%= t("admin.mark_as_read") %>',
    deleteNotification: '<%= t("admin.delete_notification") %>',
    markedNotificationsAsRead: '<%= t("admin.marked_notifications_as_read") %>',
    failedToMarkAsRead: '<%= t("admin.failed_to_mark_as_read") %>',
    errorMarkingAsRead: '<%= t("admin.error_marking_as_read") %>',
    failedToCleanup: '<%= t("admin.failed_to_cleanup") %>',
    errorCleaningUp: '<%= t("admin.error_cleaning_up") %>',
    testNotificationCreated: '<%= t("admin.test_notification_created") %>',
    failedToCreateTest: '<%= t("admin.failed_to_create_test") %>',
    errorCreatingTest: '<%= t("admin.error_creating_test") %>',
    failedToCreateSamples: '<%= t("admin.failed_to_create_samples") %>',
    errorCreatingSamples: '<%= t("admin.error_creating_samples") %>',
    notificationDeleted: '<%= t("admin.notification_deleted") %>',
    failedToDelete: '<%= t("admin.failed_to_delete") %>',
    errorDeleting: '<%= t("admin.error_deleting") %>',
    page: '<%= t("admin.page") %>',
    loadingNotifications: '<%= t("admin.loading_notifications") %>',
    noNotificationsFound: '<%= t("admin.no_notifications_found") %>',
    failedToLoad: '<%= t("admin.failed_to_load") %>',
    errorLoading: '<%= t("admin.error_loading") %>',
    unableToConnect: '<%= t("admin.unable_to_connect") %>'
  };

  let currentPage = 1;
  let socket = null;
  let notifications = [];

  // Debug helper function
  function updateDebugStatus(message) {
    const debugStatus = document.getElementById('debugStatus');
    const debugMessage = document.getElementById('debugMessage');
    if (debugStatus && debugMessage) {
      debugStatus.classList.remove('hidden');
      debugMessage.textContent = message;
      // console.log('🐛 Debug:', message); // Removed for production
    }
  }

  // Load notifications function - moved up for proper hoisting
  const loadNotifications = async function() {
    // console.log('🔍 loadNotifications called'); // Removed for production
    updateDebugStatus('Loading notifications...');

    const loadingIndicator = document.getElementById('loadingIndicator');
    const emptyState = document.getElementById('emptyState');

    try {
      // Check if elements exist
      if (!loadingIndicator) {
        console.error('loadingIndicator element not found');
        updateDebugStatus('❌ loadingIndicator element not found');
        return;
      }
      if (!emptyState) {
        console.error('emptyState element not found');
        updateDebugStatus('❌ emptyState element not found');
        return;
      }

      // console.log('🔍 loadNotifications called'); // Removed for production
      updateDebugStatus('Elements found, showing loading indicator...');
      // Show loading indicator
      loadingIndicator.classList.remove('hidden');
      emptyState.classList.add('hidden');

      const params = new URLSearchParams({
        page: currentPage,
        limit: 20
      });
      console.log('📋 Query params:', params.toString());
      updateDebugStatus('Getting filter elements...');

      // Add filters
      // console.log('🔧 Getting filter elements...'); // Removed for production
      const typeFilterEl = document.getElementById('typeFilter');
      const categoryFilterEl = document.getElementById('categoryFilter');
      const priorityFilterEl = document.getElementById('priorityFilter');
      const statusFilterEl = document.getElementById('statusFilter');

      // console.log('🔧 Filter elements:', { typeFilterEl, categoryFilterEl, priorityFilterEl, statusFilterEl }); // Removed for production
      const typeFilter = typeFilterEl?.value || '';
      const categoryFilter = categoryFilterEl?.value || '';
      const priorityFilter = priorityFilterEl?.value || '';
      const statusFilter = statusFilterEl?.value || '';

      // console.log('🔧 Filter values:', { typeFilter, categoryFilter, priorityFilter, statusFilter }); // Removed for production
      if (typeFilter) params.append('type', typeFilter);
      if (categoryFilter) params.append('category', categoryFilter);
      if (priorityFilter) params.append('priority', priorityFilter);
      if (statusFilter) params.append('is_read', statusFilter);

      // console.log('🌐 Making API call to:', `/api/notifications/admin?${params}`); // Removed for production
      updateDebugStatus('Making API call...');

      const response = await fetch(`/api/notifications/admin?${params}`, {
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      // console.log('📡 Response status:', response.status); // Removed for production
      updateDebugStatus(`API response received: ${response.status}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      // console.log('📊 Response data:', data); // Removed for production
      if (data.success) {
        // console.log('✅ API call successful, processing data...'); // Removed for production
        updateDebugStatus(`✅ API Success: ${data.notifications.length} notifications received`);
        notifications = data.notifications;
        // console.log('📊 About to render notifications:', data.notifications.length); // Removed for production
        renderNotifications(data.notifications);
        updateStats(data.stats, data.unreadCount);
        updatePagination(data.pagination);

        // Add visual indicator that data was loaded
        document.title = `Admin Notifications (${data.notifications.length})`;
        updateDebugStatus(`✅ Rendered ${data.notifications.length} notifications successfully`);

        // Hide debug status after successful load
        setTimeout(() => {
          const debugStatus = document.getElementById('debugStatus');
          if (debugStatus) {
            debugStatus.classList.add('hidden');
          }
        }, 2000);
      } else {
        console.error('API Error:', data.error);
        updateDebugStatus(`❌ API Error: ${data.error || 'Unknown error'}`);
        showToast(translations.failedToLoad + ': ' + (data.error || 'Unknown error'), 'error');
        showEmptyState(translations.failedToLoad + ': ' + (data.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
      const errorMessage = error.message.includes('Failed to fetch')
        ? translations.unableToConnect
        : error.message;
      updateDebugStatus(`❌ Error: ${errorMessage}`);
      showToast(translations.errorLoading + ': ' + errorMessage, 'error');
      showEmptyState(translations.errorLoading + ': ' + errorMessage);
    } finally {
      // Hide loading indicator
      if (loadingIndicator) {
        loadingIndicator.classList.add('hidden');
      }
    }
  };

  // Initialize Socket.IO connection
  function initializeSocket() {
    socket = io();

    // Join admin notification room
    socket.emit('admin:join', {
      isAdmin: true,
      userId: '<%= locals.session?.userId %>'
    });

    // Listen for real-time notification events
    socket.on('notification:new', (notification) => {
      addNotificationToList(notification, true);
      loadNotifications(); // Reload to get updated stats
      showToast(translations.newNotificationReceived, 'info');
    });

    socket.on('notification:updated', (data) => {
      updateNotificationInList(data.id, { isRead: data.isRead });
      loadNotifications(); // Reload to get updated stats
    });

    socket.on('notification:deleted', (data) => {
      removeNotificationFromList(data.id);
      loadNotifications(); // Reload to get updated stats
    });

    socket.on('notification:allMarkedRead', () => {
      markAllNotificationsAsRead();
      loadNotifications(); // Reload to get updated stats
    });
  }



  // Render notifications
  function renderNotifications(notifications) {
    // console.log('🎨 renderNotifications called with:', notifications); // Removed for production
    // console.log('📊 Notifications count:', notifications.length); // Removed for production
    const container = document.getElementById('notificationsList');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const emptyState = document.getElementById('emptyState');

    // console.log('📋 Container elements check:'); // Removed for production
    // console.log('  - container:', !!container); // Removed for production
    // console.log('  - loadingIndicator:', !!loadingIndicator); // Removed for production
    // console.log('  - emptyState:', !!emptyState); // Removed for production
    loadingIndicator.classList.add('hidden');

    if (notifications.length === 0) {
      // console.log('📭 No notifications to display'); // Removed for production
      container.innerHTML = '';
      emptyState.classList.remove('hidden');
      return;
    }

    // console.log('📬 Rendering', notifications.length, 'notifications'); // Removed for production
    emptyState.classList.add('hidden');

    try {
      const html = notifications.map(notification => createNotificationHTML(notification)).join('');
      // console.log('🔧 Generated HTML length:', html.length); // Removed for production
      container.innerHTML = html;
      // console.log('✅ Notifications rendered successfully'); // Removed for production
    } catch (error) {
      console.error('❌ Error rendering notifications:', error);
      alert('Error rendering notifications: ' + error.message);
    }
  }

  // Create notification HTML
  function createNotificationHTML(notification) {

    const typeIcons = {
      info: 'ti-info-circle',
      warning: 'ti-alert-triangle',
      error: 'ti-alert-circle',
      success: 'ti-check-circle'
    };

    const typeColors = {
      info: 'text-primary',
      warning: 'text-yellow-400',
      error: 'text-red-400',
      success: 'text-green-400'
    };

    const priorityColors = {
      low: 'bg-gray-500',
      normal: 'bg-primary',
      high: 'bg-orange-500',
      critical: 'bg-red-500'
    };

    const isRead = notification.is_read;
    const readClass = isRead ? 'opacity-60' : '';

    return `
      <div class="notification-item p-6 hover:bg-dark-700 transition-colors ${readClass}" data-id="${notification.id}">
        <div class="flex items-start justify-between">
          <div class="flex items-start space-x-4 flex-1">
            <div class="w-10 h-10 rounded-lg bg-dark-700 flex items-center justify-center">
              <i class="ti ${typeIcons[notification.type]} ${typeColors[notification.type]} text-lg"></i>
            </div>
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-1">
                <h4 class="font-medium text-white">${notification.title}</h4>
                <span class="px-2 py-1 text-xs rounded-full ${priorityColors[notification.priority]} text-white">
                  ${notification.priority.toUpperCase()}
                </span>
                <span class="px-2 py-1 text-xs rounded-full bg-gray-600 text-gray-300">
                  ${notification.category.toUpperCase()}
                </span>
                ${!isRead ? '<span class="w-2 h-2 bg-blue-500 rounded-full"></span>' : ''}
              </div>
              <p class="text-gray-300 text-sm mb-2">${notification.message}</p>
              <p class="text-gray-500 text-xs">${new Date(notification.created_at).toLocaleString()}</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            ${!isRead ? `<button class="mark-read-btn text-blue-400 hover:text-blue-300 p-1" data-id="${notification.id}" title="${translations.markAsRead}">
              <i class="ti ti-check"></i>
            </button>` : ''}
            <button class="delete-btn text-red-400 hover:text-red-300 p-1" data-id="${notification.id}" title="${translations.deleteNotification}">
              <i class="ti ti-trash"></i>
            </button>
          </div>
        </div>
      </div>
    `;
  }

  // Update stats
  function updateStats(stats, unreadCount) {
    if (stats && unreadCount !== undefined) {
      document.getElementById('totalCount').textContent = stats.total || 0;
      document.getElementById('unreadCount').textContent = unreadCount || 0;
      document.getElementById('criticalCount').textContent = stats.byPriority?.critical?.total || 0;
      document.getElementById('systemCount').textContent = stats.byCategory?.system?.total || 0;
    }
  }

  // Update pagination
  function updatePagination(pagination) {
    const paginationContainer = document.getElementById('pagination');
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');
    const pageInfo = document.getElementById('pageInfo');

    if (pagination.page > 1 || pagination.hasMore) {
      paginationContainer.classList.remove('hidden');
      prevBtn.disabled = pagination.page <= 1;
      nextBtn.disabled = !pagination.hasMore;
      pageInfo.textContent = `${translations.page} ${pagination.page}`;
    } else {
      paginationContainer.classList.add('hidden');
    }
  }

  // Event handlers
  document.addEventListener('DOMContentLoaded', function() {
    // console.log('🚀 DOM Content Loaded'); // Removed for production
    updateDebugStatus('DOM Content Loaded');

    // Check if required elements exist
    const loadingIndicator = document.getElementById('loadingIndicator');
    const emptyState = document.getElementById('emptyState');
    const notificationsList = document.getElementById('notificationsList');

    // console.log('📋 Required elements check:'); // Removed for production
    // console.log('  - loadingIndicator:', !!loadingIndicator); // Removed for production
    // console.log('  - emptyState:', !!emptyState); // Removed for production
    // console.log('  - notificationsList:', !!notificationsList); // Removed for production
    if (!loadingIndicator || !emptyState || !notificationsList) {
      updateDebugStatus('❌ Missing required DOM elements');
      notifications.error('Page Error', 'Missing required DOM elements for notifications page');
      return;
    }

    updateDebugStatus('Initializing Socket.IO...');
    initializeSocket();
    // console.log('🔌 Socket initialized, calling loadNotifications...'); // Removed for production
    updateDebugStatus('Loading notifications...');

    try {
      // console.log('🔍 About to call loadNotifications function...'); // Removed for production
      loadNotifications();
      // console.log('🔍 loadNotifications function called successfully'); // Removed for production
    } catch (callError) {
      console.error('❌ Error calling loadNotifications:', callError);
      updateDebugStatus(`❌ Error calling loadNotifications: ${callError.message}`);
      notifications.error('Loading Error', 'Error calling loadNotifications: ' + callError.message);
    }

    // Filter change handlers
    ['typeFilter', 'categoryFilter', 'priorityFilter', 'statusFilter'].forEach(id => {
      document.getElementById(id).addEventListener('change', () => {
        currentPage = 1;
        loadNotifications();
      });
    });

    // Mark all as read
    document.getElementById('markAllReadBtn').addEventListener('click', async () => {
      if (!confirm(translations.markAllReadConfirm)) {
        return;
      }

      try {
        const response = await fetch('/api/notifications/admin/mark-all-read', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        const data = await response.json();

        if (data.success) {
          showToast(`${data.markedCount} ${translations.markedNotificationsAsRead}`, 'success');
          loadNotifications();
        } else {
          showToast(translations.failedToMarkAsRead, 'error');
        }
      } catch (error) {
        console.error('Error marking all as read:', error);
        showToast(translations.errorMarkingAsRead, 'error');
      }
    });

    // Cleanup old notifications
    document.getElementById('cleanupBtn').addEventListener('click', async () => {
      if (!confirm(translations.cleanupOldConfirm)) {
        return;
      }

      try {
        const response = await fetch('/api/notifications/admin/cleanup', {
          method: 'POST',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ daysToKeep: 30 })
        });
        const data = await response.json();

        if (data.success) {
          showToast(data.message, 'success');
          loadNotifications();
        } else {
          showToast(translations.failedToCleanup, 'error');
        }
      } catch (error) {
        console.error('Error cleaning up notifications:', error);
        showToast(translations.errorCleaningUp, 'error');
      }
    });

    // Test notification
    document.getElementById('testNotificationBtn').addEventListener('click', () => {
      document.getElementById('testNotificationModal').classList.remove('hidden');
    });

    // Create sample notifications
    document.getElementById('createSamplesBtn').addEventListener('click', async () => {
      const confirmed = await notifications.confirm(
        translations.createSamplesConfirm,
        'Create Sample Notifications',
        {
          confirmText: 'Create Samples',
          type: 'info'
        }
      );

      if (!confirmed) {
        return;
      }

      const loading = notifications.loading('Creating samples...', 'Please wait while we create sample notifications');

      try {
        const response = await fetch('/api/notifications/admin/create-samples', {
          method: 'POST',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' }
        });
        const data = await response.json();
        loading.close();

        if (data.success) {
          notifications.success('Samples Created', data.message);
          loadNotifications();
        } else {
          notifications.error('Creation Failed', translations.failedToCreateSamples);
        }
      } catch (error) {
        loading.close();
        console.error('Error creating sample notifications:', error);
        notifications.error('Network Error', translations.errorCreatingSamples);
      }
    });

    document.getElementById('cancelTestBtn').addEventListener('click', () => {
      document.getElementById('testNotificationModal').classList.add('hidden');
    });

    document.getElementById('testNotificationForm').addEventListener('submit', async (e) => {
      e.preventDefault();

      try {
        const response = await fetch('/api/notifications/admin/test', {
          method: 'POST',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            title: document.getElementById('testTitle').value,
            message: document.getElementById('testMessage').value,
            type: document.getElementById('testType').value,
            priority: document.getElementById('testPriority').value
          })
        });
        const data = await response.json();

        if (data.success) {
          showToast(translations.testNotificationCreated, 'success');
          document.getElementById('testNotificationModal').classList.add('hidden');
          loadNotifications();
        } else {
          showToast(translations.failedToCreateTest, 'error');
        }
      } catch (error) {
        console.error('Error creating test notification:', error);
        showToast(translations.errorCreatingTest, 'error');
      }
    });

    // Pagination
    document.getElementById('prevPage').addEventListener('click', () => {
      if (currentPage > 1) {
        currentPage--;
        loadNotifications();
      }
    });

    document.getElementById('nextPage').addEventListener('click', () => {
      currentPage++;
      loadNotifications();
    });

    // Notification actions (using event delegation)
    document.getElementById('notificationsList').addEventListener('click', async (e) => {
      const markReadBtn = e.target.closest('.mark-read-btn');
      const deleteBtn = e.target.closest('.delete-btn');

      if (markReadBtn) {
        const id = markReadBtn.dataset.id;
        try {
          const response = await fetch(`/api/notifications/admin/${id}/mark-read`, {
            method: 'POST',
            credentials: 'include',
            headers: { 'Content-Type': 'application/json' }
          });
          const data = await response.json();

          if (data.success) {
            updateNotificationInList(id, { isRead: true });
            loadNotifications(); // Reload to get updated stats
          } else {
            showToast(translations.failedToMarkAsRead, 'error');
          }
        } catch (error) {
          console.error('Error marking notification as read:', error);
          showToast(translations.errorMarkingAsRead, 'error');
        }
      }

      if (deleteBtn) {
        const id = deleteBtn.dataset.id;
        if (!confirm(translations.deleteNotificationConfirm)) {
          return;
        }

        try {
          const response = await fetch(`/api/notifications/admin/${id}`, {
            method: 'DELETE',
            credentials: 'include',
            headers: { 'Content-Type': 'application/json' }
          });
          const data = await response.json();

          if (data.success) {
            removeNotificationFromList(id);
            loadNotifications(); // Reload to get updated stats
            showToast(translations.notificationDeleted, 'success');
          } else {
            showToast(translations.failedToDelete, 'error');
          }
        } catch (error) {
          console.error('Error deleting notification:', error);
          showToast(translations.errorDeleting, 'error');
        }
      }
    });
  });

  // Helper functions
  function addNotificationToList(notification, prepend = false) {
    const container = document.getElementById('notificationsList');
    const html = createNotificationHTML(notification);

    if (prepend) {
      container.insertAdjacentHTML('afterbegin', html);
    } else {
      container.insertAdjacentHTML('beforeend', html);
    }
  }

  function updateNotificationInList(id, updates) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item && updates.isRead) {
      item.classList.add('opacity-60');
      const unreadDot = item.querySelector('.w-2.h-2.bg-blue-500');
      const markReadBtn = item.querySelector('.mark-read-btn');
      if (unreadDot) unreadDot.remove();
      if (markReadBtn) markReadBtn.remove();
    }
  }

  function removeNotificationFromList(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
      item.remove();
    }
  }

  function markAllNotificationsAsRead() {
    const items = document.querySelectorAll('.notification-item');
    items.forEach(item => {
      item.classList.add('opacity-60');
      const unreadDot = item.querySelector('.w-2.h-2.bg-blue-500');
      const markReadBtn = item.querySelector('.mark-read-btn');
      if (unreadDot) unreadDot.remove();
      if (markReadBtn) markReadBtn.remove();
    });
  }

  function showToast(message, type = 'info') {
    // Simple toast implementation
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg text-white ${
      type === 'success' ? 'bg-green-600' :
      type === 'error' ? 'bg-red-600' :
      type === 'warning' ? 'bg-yellow-600' :
      'bg-blue-600'
    }`;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.remove();
    }, 3000);
  }

  function showEmptyState(message) {
    const container = document.getElementById('notificationsList');
    const emptyState = document.getElementById('emptyState');

    container.innerHTML = '';
    emptyState.classList.remove('hidden');

    // Update empty state message if provided
    if (message) {
      const emptyMessage = emptyState.querySelector('p');
      if (emptyMessage) {
        emptyMessage.textContent = message;
      }
    }
  }


</script>
