#!/usr/bin/env node

/**
 * Check Expired Subscriptions Cron Job
 * Automatically handles expired subscriptions by:
 * 1. Downgrading users to Preview plan
 * 2. Deleting expired subscription history
 * 3. Stopping active streams
 * 4. Cleaning up resources
 */

const { db } = require('../db/database');
const Subscription = require('../models/Subscription');

class ExpiredSubscriptionChecker {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.stats = {
      checked: 0,
      processed: 0,
      errors: 0,
      downgraded: []
    };
  }

  async run() {
    try {
      console.log('🔍 Starting expired subscription check...');
      console.log(`Environment: ${this.isProduction ? 'PRODUCTION' : 'DEVELOPMENT'}`);
      console.log(`Time: ${new Date().toISOString()}`);
      console.log('');

      const result = await Subscription.checkAndHandleExpiredSubscriptions();
      
      this.stats.checked = result.processed || 0;
      this.stats.processed = result.expired?.length || 0;
      this.stats.downgraded = result.expired || [];

      await this.printSummary();
      await this.logToDatabase();

    } catch (error) {
      console.error('❌ Expired subscription check failed:', error.message);
      this.stats.errors++;
      process.exit(1);
    }
  }

  async printSummary() {
    console.log('');
    console.log('📋 Expired Subscription Check Summary:');
    console.log('=====================================');
    console.log(`Subscriptions checked: ${this.stats.checked}`);
    console.log(`Users processed: ${this.stats.processed}`);
    console.log(`Errors: ${this.stats.errors}`);
    console.log('');

    if (this.stats.downgraded.length > 0) {
      console.log('👥 Users downgraded to Preview plan:');
      this.stats.downgraded.forEach(user => {
        console.log(`  - ${user.username} (${user.planName}) expired: ${new Date(user.expiredDate).toLocaleDateString()}`);
      });
    } else {
      console.log('✅ No expired subscriptions found');
    }

    console.log('');
    console.log(`✅ Check completed at ${new Date().toISOString()}`);
  }

  async logToDatabase() {
    if (this.stats.processed > 0) {
      try {
        // Log the expired subscription check to database for audit
        const logEntry = {
          timestamp: new Date().toISOString(),
          type: 'expired_subscription_check',
          processed: this.stats.processed,
          users: this.stats.downgraded.map(u => ({
            userId: u.userId,
            username: u.username,
            expiredPlan: u.planName,
            expiredDate: u.expiredDate
          }))
        };

        // You can add database logging here if needed
        console.log('📝 Audit log:', JSON.stringify(logEntry, null, 2));
        
      } catch (error) {
        console.error('⚠️ Failed to log to database:', error.message);
      }
    }
  }
}

// Handle process signals gracefully
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Run the checker
const checker = new ExpiredSubscriptionChecker();
checker.run().catch(error => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
});
