/**
 * Fix specific user with expired subscription
 * This script handles users who still show incorrect slot limits after expiration
 */

const { db } = require('../db/database');
const Subscription = require('../models/Subscription');

async function fixExpiredUser(username) {
  console.log(`🔍 Checking user: ${username}\n`);

  try {
    // Get user details
    const user = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM users WHERE username = ?',
        [username],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!user) {
      console.log('❌ User not found!');
      return;
    }

    console.log('👤 User Details:', {
      id: user.id,
      username: user.username,
      plan_type: user.plan_type,
      max_streaming_slots: user.max_streaming_slots,
      max_storage_gb: user.max_storage_gb
    });

    // Get user's current subscription including expired ones
    const subscription = await Subscription.getUserSubscriptionIncludingExpired(user.id);
    
    console.log('\n📋 Subscription Details:', subscription ? {
      plan_name: subscription.plan_name,
      status: subscription.status,
      end_date: subscription.end_date,
      isExpired: subscription.isExpired,
      max_streaming_slots: subscription.max_streaming_slots,
      max_storage_gb: subscription.max_storage_gb
    } : 'No subscription found');

    // Check if subscription is expired
    if (subscription && subscription.isExpired) {
      console.log('\n⚠️ User has expired subscription! Processing...');
      
      // Handle expired subscription
      const success = await Subscription.handleExpiredSubscription(user.id);
      
      if (success) {
        console.log('✅ Successfully processed expired subscription');
        
        // Verify the fix
        const updatedUser = await new Promise((resolve, reject) => {
          db.get(
            'SELECT * FROM users WHERE id = ?',
            [user.id],
            (err, row) => {
              if (err) reject(err);
              else resolve(row);
            }
          );
        });

        console.log('\n✅ Updated User Details:', {
          plan_type: updatedUser.plan_type,
          max_streaming_slots: updatedUser.max_streaming_slots,
          max_storage_gb: updatedUser.max_storage_gb
        });

        // Check new subscription
        const newSubscription = await Subscription.getUserSubscription(user.id);
        console.log('\n✅ New Active Subscription:', newSubscription ? {
          plan_name: newSubscription.plan_name,
          status: newSubscription.status,
          end_date: newSubscription.end_date
        } : 'No active subscription (Preview plan)');

      } else {
        console.log('❌ Failed to process expired subscription');
      }
    } else if (subscription && !subscription.isExpired) {
      console.log('\n✅ User has active subscription - no action needed');
    } else {
      console.log('\n⚠️ User has no subscription - ensuring Preview plan settings...');
      
      // Make sure user is properly set to Preview plan
      const previewPlan = await Subscription.getPlanByName('Preview');
      if (previewPlan) {
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE users SET plan_type = ?, max_streaming_slots = ?, max_storage_gb = ? WHERE id = ?',
            ['Preview', previewPlan.max_streaming_slots, previewPlan.max_storage_gb, user.id],
            function (err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });

        // Create Preview subscription if doesn't exist
        const activeSubscription = await Subscription.getUserSubscription(user.id);
        if (!activeSubscription) {
          await Subscription.createSubscription({
            user_id: user.id,
            plan_id: previewPlan.id,
            status: 'active',
            end_date: null, // Preview plan has no expiration
            payment_method: 'free'
          });
        }

        console.log('✅ User set to Preview plan with correct limits');
      }
    }

    console.log('\n🎉 User fix completed!');

  } catch (error) {
    console.error('❌ Error fixing user:', error);
  }
}

async function checkAllExpiredUsers() {
  console.log('🔍 Checking all users with expired subscriptions...\n');

  try {
    // Find users with expired subscriptions
    const expiredUsers = await new Promise((resolve, reject) => {
      db.all(`
        SELECT u.id, u.username, u.plan_type, u.max_streaming_slots, u.max_storage_gb,
               us.end_date, us.status, sp.name as subscription_plan_name
        FROM users u
        LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
        LEFT JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.end_date < datetime('now') AND sp.name != 'Preview'
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`Found ${expiredUsers.length} users with expired subscriptions`);

    for (const user of expiredUsers) {
      console.log(`\n📉 Processing ${user.username}...`);
      await fixExpiredUser(user.username);
    }

    console.log('\n✅ All expired users processed!');

  } catch (error) {
    console.error('❌ Error checking expired users:', error);
  }
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage:');
    console.log('  node scripts/fix-expired-user.js <username>  - Fix specific user');
    console.log('  node scripts/fix-expired-user.js --all       - Fix all expired users');
    process.exit(1);
  }

  if (args[0] === '--all') {
    checkAllExpiredUsers().then(() => {
      console.log('\n✅ All done!');
      process.exit(0);
    }).catch(error => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
  } else {
    const username = args[0];
    fixExpiredUser(username).then(() => {
      console.log('\n✅ All done!');
      process.exit(0);
    }).catch(error => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
  }
}

module.exports = { fixExpiredUser, checkAllExpiredUsers };
