<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white"><%= t('admin.user_management_title') %></h1>
      <p class="text-gray-400 mt-1"><%= t('admin.user_management_subtitle') %></p>
    </div>
    <div class="flex items-center space-x-4">
      <div id="bulkActions" class="hidden flex items-center space-x-2">
        <select id="bulkActionSelect" class="bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg">
          <option value=""><%= t('admin.select_action') %></option>
          <option value="activate"><%= t('admin.activate_users') %></option>
          <option value="deactivate"><%= t('admin.deactivate_users') %></option>
          <option value="delete"><%= t('admin.delete_users') %></option>
          <option value="change_plan"><%= t('admin.change_plan') %></option>
        </select>
        <select id="bulkPlanSelect" class="bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg hidden">
          <% plans.forEach(function(plan) { %>
            <option value="<%= plan.id %>"><%= plan.name %></option>
          <% }); %>
        </select>
        <button onclick="executeBulkAction()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
          <%= t('admin.apply') %>
        </button>
        <button onclick="clearSelection()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
          <%= t('admin.clear') %>
        </button>
      </div>
      <button onclick="refreshUsers()" class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg transition-colors">
        <i class="ti ti-refresh mr-2"></i>
        <%= t('admin.refresh') %>
      </button>
    </div>
  </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.total_users') %></p>
        <p class="text-2xl font-bold text-white"><%= totalUsers || stats.total_users || 0 %></p>
        <% if (totalPages > 1) { %>
          <p class="text-xs text-gray-500 mt-1">Page <%= currentPage %> of <%= totalPages %></p>
        <% } %>
      </div>
      <div class="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-users text-primary text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.active_users') %></p>
        <p class="text-2xl font-bold text-white"><%= stats.active_users || 0 %></p>
      </div>
      <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-user-check text-green-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.admins') %></p>
        <p class="text-2xl font-bold text-white"><%= stats.admin_users || 0 %></p>
      </div>
      <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-shield text-red-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.premium_users') %></p>
        <p class="text-2xl font-bold text-white"><%= stats.premium_users || 0 %></p>
      </div>
      <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-crown text-yellow-400 text-xl"></i>
      </div>
    </div>
  </div>
</div>

<!-- Users Table -->
<div class="bg-dark-800 rounded-lg border border-gray-700">
  <div class="p-6 border-b border-gray-700">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-white"><%= t('admin.all_users') %></h3>
      <div class="flex items-center space-x-4">
        <form method="GET" action="/admin/users" class="relative">
          <input type="text" name="search" id="searchUsers" placeholder="<%= t('admin.search_users') %>"
                 value="<%= search || '' %>"
                 class="bg-dark-700 border border-gray-600 text-white pl-9 pr-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
          <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          <% if (search) { %>
            <a href="/admin/users" class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white">
              <i class="ti ti-x"></i>
            </a>
          <% } %>
        </form>
      </div>
    </div>
  </div>

  <div class="overflow-x-auto">
    <table class="min-w-full">
      <thead class="bg-gray-700">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()"
                   class="rounded border-gray-600 bg-dark-700 text-primary focus:ring-primary">
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.user') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.role') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.plan') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.storage') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.status') %></th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.actions') %></th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-700">
        <% users.forEach(function(user) { %>
          <tr class="hover:bg-dark-700/50 transition-colors">
            <td class="px-6 py-4 whitespace-nowrap">
              <input type="checkbox" class="user-checkbox rounded border-gray-600 bg-dark-700 text-primary focus:ring-primary"
                     value="<%= user.id %>" onchange="updateBulkActions()">
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                  <i class="ti ti-user text-white"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-white"><%= user.username %></div>
                  <div class="text-sm text-gray-400"><%= user.email || t('admin.no_email') %></div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= user.role === 'admin' ? 'bg-red-100 text-red-800' : user.role === 'moderator' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800' %>">
                <%= user.role %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white">
                <%= user.plan_type %>
                <% if (user.trial_end_date && new Date(user.trial_end_date) > new Date()) { %>
                  <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 ml-1">
                    <%= t('admin.active_trial') %>
                  </span>
                <% } %>
              </div>
              <div class="text-sm text-gray-400">
                <%= user.max_streaming_slots === -1 ? t('admin.unlimited') : user.max_streaming_slots %> <%= t('admin.slots') %>,
                <% if (user.max_storage_gb <= 1) { %>
                  <%= Math.round(user.max_storage_gb * 1024) %>MB
                <% } else { %>
                  <%= user.max_storage_gb %>GB
                <% } %>
                <% if (user.trial_end_date && new Date(user.trial_end_date) > new Date()) { %>
                  <br><span class="text-xs text-yellow-400">
                    <%= t('admin.trial_expires_on') %>: <%= new Date(user.trial_end_date).toLocaleDateString() %>
                  </span>
                <% } %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white">
                <% var usedGB = user.used_storage_gb || 0; %>
                <% if (user.max_storage_gb <= 1 && usedGB <= 1) { %>
                  <%= Math.round(usedGB * 1024) %><%= t('admin.mb_used') %>
                <% } else { %>
                  <%= usedGB.toFixed(2) %><%= t('admin.gb_used') %>
                <% } %>
              </div>
              <div class="w-full bg-gray-700 rounded-full h-2 mt-1">
                <% var percentage = user.max_storage_gb > 0 ? ((user.used_storage_gb || 0) / user.max_storage_gb * 100) : 0; %>
                <div class="bg-primary h-2 rounded-full" style="width: '<%= Math.min(percentage, 100) %>'"></div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                <%= user.is_active ? t('admin.user_active') : t('admin.inactive') %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex items-center justify-end space-x-2">
                <button onclick="editUser('<%= user.id %>')" class="text-primary hover:text-primary-light" title="<%= t('admin.edit_user') %>">
                  <i class="ti ti-edit"></i>
                </button>
                <% if (user.plan_type === 'Preview' && user.role !== 'admin') { %>
                  <% if (user.trial_end_date && new Date(user.trial_end_date) > new Date()) { %>
                    <button onclick="removeTrialUser('<%= user.id %>', '<%= user.username %>')" class="text-orange-400 hover:text-orange-300" title="<%= t('admin.remove_trial') %>">
                      <i class="ti ti-gift-off"></i>
                    </button>
                  <% } else { %>
                    <button onclick="giveTrialUser('<%= user.id %>', '<%= user.username %>')" class="text-yellow-400 hover:text-yellow-300" title="<%= t('admin.give_trial') %>">
                      <i class="ti ti-gift"></i>
                    </button>
                  <% } %>
                <% } %>
                <% if (user.is_active) { %>
                  <button onclick="toggleUserStatus('<%= user.id %>', 'false')" class="text-red-400 hover:text-red-300" title="<%= t('admin.deactivate_user') %>">
                    <i class="ti ti-user-off"></i>
                  </button>
                <% } else { %>
                  <button onclick="toggleUserStatus('<%= user.id %>', 'true')" class="text-green-400 hover:text-green-300" title="<%= t('admin.activate_user') %>">
                    <i class="ti ti-user-check"></i>
                  </button>
                <% } %>
                <% if (user.role !== 'admin') { %>
                  <button onclick="deleteUser('<%= user.id %>', '<%= user.username %>')" class="text-red-500 hover:text-red-400" title="<%= t('admin.delete_user') %>">
                    <i class="ti ti-trash"></i>
                  </button>
                <% } %>
              </div>
            </td>
          </tr>
        <% }); %>
      </tbody>
    </table>

    <!-- No results message -->
    <% if (users.length === 0) { %>
      <div class="p-8 text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-gray-700 rounded-full flex items-center justify-center">
          <i class="ti ti-users text-2xl text-gray-400"></i>
        </div>
        <% if (search) { %>
          <h3 class="text-lg font-medium text-white mb-2">No users found</h3>
          <p class="text-gray-400 mb-4">No users match your search criteria "<%= search %>"</p>
          <a href="/admin/users" class="inline-flex items-center gap-2 text-primary hover:text-primary-light">
            <i class="ti ti-arrow-left"></i>
            Clear search and show all users
          </a>
        <% } else { %>
          <h3 class="text-lg font-medium text-white mb-2">No users found</h3>
          <p class="text-gray-400">No users have been registered yet.</p>
        <% } %>
      </div>
    <% } %>
  </div>

  <!-- Pagination -->
  <% if (totalPages > 1) { %>
    <div class="p-6 border-t border-gray-700">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-400">
          <% if (search) { %>
            Showing <%= ((currentPage - 1) * limit) + 1 %> to <%= Math.min(currentPage * limit, totalUsers) %> of <%= totalUsers %> users
            <span class="text-primary">matching "<%= search %>"</span>
          <% } else { %>
            Showing <%= ((currentPage - 1) * limit) + 1 %> to <%= Math.min(currentPage * limit, totalUsers) %> of <%= totalUsers %> users
          <% } %>
        </div>

        <div class="flex items-center space-x-2">
          <!-- Previous Button -->
          <% if (hasPrevPage) { %>
            <a href="/admin/users?page=<%= prevPage %><%= search ? '&search=' + encodeURIComponent(search) : '' %>"
               class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
              <i class="ti ti-chevron-left"></i>
            </a>
          <% } else { %>
            <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 text-gray-500 cursor-not-allowed">
              <i class="ti ti-chevron-left"></i>
            </span>
          <% } %>

          <!-- Page Numbers -->
          <%
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);

            if (endPage - startPage < 4 && startPage > 1) {
              startPage = Math.max(1, endPage - 4);
            }
          %>

          <% for (let i = startPage; i <= endPage; i++) { %>
            <% if (i === currentPage) { %>
              <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-primary text-white font-medium">
                <%= i %>
              </span>
            <% } else { %>
              <a href="/admin/users?page=<%= i %><%= search ? '&search=' + encodeURIComponent(search) : '' %>"
                 class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
                <%= i %>
              </a>
            <% } %>
          <% } %>

          <!-- Next Button -->
          <% if (hasNextPage) { %>
            <a href="/admin/users?page=<%= nextPage %><%= search ? '&search=' + encodeURIComponent(search) : '' %>"
               class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
              <i class="ti ti-chevron-right"></i>
            </a>
          <% } else { %>
            <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 text-gray-500 cursor-not-allowed">
              <i class="ti ti-chevron-right"></i>
            </span>
          <% } %>
        </div>
      </div>
    </div>
  <% } %>
</div>

<!-- Edit User Modal -->
<div id="editUserModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-dark-800 rounded-lg border border-gray-700 w-full max-w-2xl">
      <div class="p-6 border-b border-gray-700">
        <div class="flex items-center justify-between">
          <h3 id="editModalTitle" class="text-lg font-semibold text-white"><%= t('admin.edit_user') %></h3>
          <button onclick="closeEditUserModal()" class="text-gray-400 hover:text-white">
            <i class="ti ti-x text-xl"></i>
          </button>
        </div>
      </div>

      <form id="editUserForm" class="p-6">
        <input type="hidden" id="editUserId" name="userId">

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.username') %></label>
            <input type="text" id="editUsername" name="username" required
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.email') %></label>
            <input type="email" id="editEmail" name="email"
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.role') %></label>
            <select id="editRole" name="role"
                    class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
              <% roles.forEach(function(role) { %>
                <option value="<%= role %>"><%= role %></option>
              <% }); %>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.plan') %></label>
            <select id="editPlan" name="plan"
                    class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
              <% plans.forEach(function(plan) { %>
                <option value="<%= plan.name %>" data-plan-id="<%= plan.id %>"><%= plan.name %></option>
              <% }); %>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.custom_end_date') %></label>
            <input type="datetime-local" id="editCustomEndDate" name="customEndDate"
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
            <p class="text-xs text-gray-400 mt-1">Leave empty for default 30 days</p>
          </div>

          <div>
            <label class="flex items-center space-x-2">
              <input type="checkbox" id="editUseProrated" name="useProrated"
                     class="rounded border-gray-600 bg-dark-700 text-primary focus:ring-primary">
              <span class="text-sm text-gray-300"><%= t('admin.use_prorated') %></span>
            </label>
            <p class="text-xs text-gray-400 mt-1">Keep current subscription end date for upgrades</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.max_streaming_slots') %></label>
            <input type="number" id="editMaxSlots" name="max_streaming_slots" min="-1"
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
            <p class="text-xs text-gray-400 mt-1"><%= t('admin.use_unlimited') %></p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.max_storage') %></label>
            <div class="flex space-x-2">
              <input type="number" id="editMaxStorage" name="max_storage_value" min="1" step="0.001"
                     class="flex-1 bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
              <select id="editStorageUnit" name="storage_unit"
                      class="bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
                <option value="gb">GB</option>
                <option value="mb">MB</option>
              </select>
            </div>
            <p class="text-xs text-gray-400 mt-1"><%= t('admin.choose_mb_smaller') %></p>
          </div>

          <div class="md:col-span-2">
            <label class="flex items-center space-x-2">
              <input type="checkbox" id="editIsActive" name="is_active"
                     class="rounded border-gray-600 bg-dark-700 text-primary focus:ring-primary">
              <span class="text-sm text-gray-300"><%= t('admin.account_active') %></span>
            </label>
          </div>
        </div>

        <div class="mt-6">
          <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.reset_password') %></label>
          <div class="flex items-center space-x-4">
            <input type="password" id="editNewPassword" name="new_password" placeholder="<%= t('admin.leave_empty_password') %>"
                   class="flex-1 bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
            <button type="button" onclick="generateRandomPassword()" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg">
              <%= t('admin.generate') %>
            </button>
          </div>
        </div>

        <div class="flex justify-end space-x-4 mt-8">
          <button type="button" onclick="closeEditUserModal()"
                  class="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
            <%= t('common.cancel') %>
          </button>
          <button type="submit"
                  class="px-6 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors">
            <%= t('admin.update_user') %>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Give Trial Modal -->
<div id="giveTrialModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-dark-800 rounded-lg border border-gray-700 w-full max-w-md">
      <div class="p-6 border-b border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-white"><%= t('admin.give_trial') %></h3>
          <button onclick="closeGiveTrialModal()" class="text-gray-400 hover:text-white">
            <i class="ti ti-x text-xl"></i>
          </button>
        </div>
      </div>

      <form id="giveTrialForm" class="p-6">
        <input type="hidden" id="trialUserId" name="userId">

        <div class="mb-4">
          <p class="text-gray-300 mb-4">
            <%= t('admin.give_trial_confirm') %>
          </p>
          <p class="text-sm text-gray-400 mb-4">
            User: <span id="trialUsername" class="text-white font-medium"></span>
          </p>
        </div>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.trial_duration_days') %></label>
            <input type="number" id="trialDuration" name="durationDays" min="1" max="365" value="7" required
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
            <p class="text-xs text-gray-400 mt-1">1-365 days</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.trial_slots') %></label>
            <input type="number" id="trialSlots" name="trialSlots" min="1" max="10" value="1" required
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.trial_storage') %></label>
            <input type="number" id="trialStorage" name="trialStorageGB" min="1" max="1000" value="50" step="1" required
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
            <p class="text-xs text-gray-400 mt-1">Storage in MB</p>
          </div>
        </div>

        <div class="flex justify-end space-x-4 mt-6">
          <button type="button" onclick="closeGiveTrialModal()"
                  class="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
            <%= t('common.cancel') %>
          </button>
          <button type="submit"
                  class="px-6 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors">
            <%= t('admin.give_trial') %>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
  function refreshUsers() {
    window.location.reload();
  }

  // Auto-submit search form with debounce
  let searchTimeout;
  document.getElementById('searchUsers').addEventListener('input', function(e) {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      e.target.form.submit();
    }, 500); // 500ms debounce
  });

  function editUser(userId) {
    // Fetch user details
    fetch(`/admin/users/${userId}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const user = data.user;

          // Populate form fields
          document.getElementById('editUserId').value = user.id;
          document.getElementById('editUsername').value = user.username;
          document.getElementById('editEmail').value = user.email || '';
          document.getElementById('editRole').value = user.role;
          document.getElementById('editPlan').value = user.plan_type;
          document.getElementById('editMaxSlots').value = user.max_streaming_slots;

          // Handle storage display - show in MB if 1GB or less, otherwise GB
          const storageGB = user.max_storage_gb;
          if (storageGB <= 1) {
            document.getElementById('editMaxStorage').value = (storageGB * 1024).toFixed(0);
            document.getElementById('editStorageUnit').value = 'mb';
          } else {
            document.getElementById('editMaxStorage').value = storageGB;
            document.getElementById('editStorageUnit').value = 'gb';
          }

          document.getElementById('editIsActive').checked = user.is_active;
          document.getElementById('editNewPassword').value = '';

          // Show modal
          document.getElementById('editUserModal').classList.remove('hidden');
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to load user details');
      });
  }

  function closeEditUserModal() {
    document.getElementById('editUserModal').classList.add('hidden');
  }

  function generateRandomPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('editNewPassword').value = password;
  }

  async function toggleUserStatus(userId, newStatus) {
    const action = newStatus === 'true' ? 'activate' : 'deactivate';
    const confirmed = await notifications.confirm(
      `Are you sure you want to ${action} this user?`,
      'Confirm User Status Change',
      {
        confirmText: action.charAt(0).toUpperCase() + action.slice(1),
        type: 'warning'
      }
    );

    if (confirmed) {
      const loading = notifications.loading('Updating user status...', 'Please wait while we update the user status');

      try {
        const response = await fetch('/admin/users/toggle-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userId, isActive: newStatus === 'true' })
        });

        const data = await response.json();
        loading.close();

        if (data.success) {
          notifications.success('Success', `User has been ${action}d successfully`);
          setTimeout(() => window.location.reload(), 1000);
        } else {
          notifications.error('Error', data.error || 'Failed to update user status');
        }
      } catch (error) {
        loading.close();
        console.error('Error:', error);
        notifications.error('Network Error', 'Failed to update user status. Please check your connection.');
      }
    }
  }

  // Bulk selection functionality
  function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');

    checkboxes.forEach(checkbox => {
      checkbox.checked = selectAll.checked;
    });

    updateBulkActions();
  }

  function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    const bulkActions = document.getElementById('bulkActions');
    const selectAll = document.getElementById('selectAll');
    const allCheckboxes = document.querySelectorAll('.user-checkbox');

    // Update select all checkbox state
    if (checkboxes.length === 0) {
      selectAll.indeterminate = false;
      selectAll.checked = false;
    } else if (checkboxes.length === allCheckboxes.length) {
      selectAll.indeterminate = false;
      selectAll.checked = true;
    } else {
      selectAll.indeterminate = true;
    }

    // Show/hide bulk actions
    if (checkboxes.length > 0) {
      bulkActions.classList.remove('hidden');
      bulkActions.classList.add('flex');
    } else {
      bulkActions.classList.add('hidden');
      bulkActions.classList.remove('flex');
    }
  }

  function clearSelection() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    const selectAll = document.getElementById('selectAll');

    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
    });
    selectAll.checked = false;
    selectAll.indeterminate = false;

    updateBulkActions();
  }

  async function executeBulkAction() {
    const action = document.getElementById('bulkActionSelect').value;
    const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);

    if (!action) {
      notifications.warning('No Action Selected', 'Please select an action to perform');
      return;
    }

    if (selectedUsers.length === 0) {
      notifications.warning('No Users Selected', 'Please select at least one user to perform the action');
      return;
    }

    let confirmMessage = '';
    let requestData = { action, userIds: selectedUsers };

    switch (action) {
      case 'activate':
        confirmMessage = `Are you sure you want to activate ${selectedUsers.length} user(s)?`;
        break;
      case 'deactivate':
        confirmMessage = `Are you sure you want to deactivate ${selectedUsers.length} user(s)?`;
        break;
      case 'delete':
        confirmMessage = `Are you sure you want to DELETE ${selectedUsers.length} user(s)? This action cannot be undone and will permanently delete all user data including streams, videos, and account information.`;
        break;
      case 'change_plan':
        const planId = document.getElementById('bulkPlanSelect').value;
        if (!planId) {
          notifications.warning('No Plan Selected', 'Please select a plan for the users');
          return;
        }
        requestData.planId = planId;
        const planName = document.getElementById('bulkPlanSelect').selectedOptions[0].text;
        confirmMessage = `Are you sure you want to change ${selectedUsers.length} user(s) to the ${planName} plan?`;
        break;
      default:
        notifications.error('Invalid Action', 'The selected action is not valid');
        return;
    }

    const confirmed = await notifications.confirm(confirmMessage, 'Confirm Bulk Action', {
      confirmText: 'Proceed',
      type: 'warning'
    });

    if (confirmed) {
      const loading = notifications.loading('Processing bulk action...', 'Please wait while we process the selected users');

      try {
        const response = await fetch('/admin/users/bulk-action', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData)
        });

        const data = await response.json();
        loading.close();

        if (data.success) {
          const successCount = data.results.filter(r => r.success).length;
          const failCount = data.results.filter(r => !r.success).length;

          let message = `Successfully processed ${successCount} user(s)`;
          if (failCount > 0) {
            message += `, ${failCount} failed`;
          }

          notifications.success('Bulk Action Complete', message);
          setTimeout(() => window.location.reload(), 1500);
        } else {
          notifications.error('Bulk Action Failed', data.error || 'Failed to execute bulk action');
        }
      } catch (error) {
        loading.close();
        console.error('Error:', error);
        notifications.error('Network Error', 'Failed to execute bulk action. Please check your connection.');
      }
    }
  }

  // Show/hide plan select based on action
  document.getElementById('bulkActionSelect').addEventListener('change', function() {
    const planSelect = document.getElementById('bulkPlanSelect');
    if (this.value === 'change_plan') {
      planSelect.classList.remove('hidden');
    } else {
      planSelect.classList.add('hidden');
    }
  });

  // Delete user function
  async function deleteUser(userId, username) {
    const confirmed = await notifications.confirm(
      `Are you sure you want to delete user "${username}"? This action cannot be undone and will permanently delete all user data including streams, videos, and account information.`,
      'Confirm User Deletion',
      {
        confirmText: 'Delete User',
        type: 'danger'
      }
    );

    if (confirmed) {
      const loading = notifications.loading('Deleting user...', 'Please wait while we delete the user and all associated data');

      try {
        const response = await fetch(`/admin/users/${userId}/delete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        const data = await response.json();
        loading.close();

        if (data.success) {
          notifications.success('User Deleted', `User "${username}" has been deleted successfully`);
          setTimeout(() => window.location.reload(), 1000);
        } else {
          notifications.error('Deletion Failed', data.error || 'Failed to delete user');
        }
      } catch (error) {
        loading.close();
        console.error('Error:', error);
        notifications.error('Network Error', 'Failed to delete user. Please check your connection.');
      }
    }
  }

  // Handle edit user form submission
  document.getElementById('editUserForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    // Handle storage conversion
    const storageValue = parseFloat(formData.get('max_storage_value'));
    const storageUnit = formData.get('storage_unit');
    let storageGB = storageValue;

    if (storageUnit === 'mb') {
      storageGB = storageValue / 1024; // Convert MB to GB
    }

    // Get plan ID from selected option
    const planSelect = document.getElementById('editPlan');
    const selectedOption = planSelect.options[planSelect.selectedIndex];
    const planId = selectedOption.getAttribute('data-plan-id');

    const userData = {
      userId: formData.get('userId'),
      username: formData.get('username'),
      email: formData.get('email'),
      role: formData.get('role'),
      plan: formData.get('plan'),
      planId: planId,
      customEndDate: formData.get('customEndDate') || null,
      useProrated: formData.has('useProrated'),
      max_streaming_slots: parseInt(formData.get('max_streaming_slots')),
      max_storage_gb: storageGB,
      is_active: formData.has('is_active')
    };

    // Only include password if it's provided
    const newPassword = formData.get('new_password');
    if (newPassword && newPassword.trim()) {
      userData.new_password = newPassword.trim();
    }

    fetch('/admin/users/edit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        notifications.success('User Updated', 'User information has been updated successfully');
        closeEditUserModal();
        setTimeout(() => window.location.reload(), 1000);
      } else {
        notifications.error('Update Failed', data.error || 'Failed to update user');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      notifications.error('Network Error', 'Failed to update user. Please check your connection.');
    });
  });

  // Give Trial functions
  function giveTrialUser(userId, username) {
    document.getElementById('trialUserId').value = userId;
    document.getElementById('trialUsername').textContent = username;
    document.getElementById('giveTrialModal').classList.remove('hidden');
  }

  function closeGiveTrialModal() {
    document.getElementById('giveTrialModal').classList.add('hidden');
    document.getElementById('giveTrialForm').reset();
  }

  // Handle give trial form submission
  document.getElementById('giveTrialForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const userId = formData.get('userId');
    const durationDays = parseInt(formData.get('durationDays'));
    const trialSlots = parseInt(formData.get('trialSlots'));
    const trialStorageGB = parseFloat(formData.get('trialStorageGB')) / 1024; // Convert MB to GB

    const loading = notifications.loading('Giving trial access...', 'Please wait while we set up the trial');

    try {
      const response = await fetch(`/admin/users/${userId}/give-trial`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          durationDays,
          trialSlots,
          trialStorageGB
        })
      });

      const data = await response.json();
      loading.close();

      if (data.success) {
        notifications.success('Trial Given', '<%= t("admin.trial_given_success") %>');
        closeGiveTrialModal();
        setTimeout(() => window.location.reload(), 1000);
      } else {
        if (data.error === 'User already has an active trial') {
          notifications.warning('Active Trial Exists', '<%= t("admin.user_has_active_trial") %>');
        } else {
          notifications.error('Failed to Give Trial', data.error || 'Failed to give trial access');
        }
      }
    } catch (error) {
      loading.close();
      console.error('Error:', error);
      notifications.error('Network Error', 'Failed to give trial access. Please check your connection.');
    }
  });

  // Remove Trial function
  async function removeTrialUser(userId, username) {
    const confirmed = await notifications.confirm(
      `<%= t("admin.remove_trial_confirm") %>`,
      'Remove Trial Access',
      {
        confirmText: '<%= t("admin.remove_trial") %>',
        type: 'warning'
      }
    );

    if (confirmed) {
      const loading = notifications.loading('Removing trial access...', 'Please wait while we remove the trial');

      try {
        const response = await fetch(`/admin/users/${userId}/remove-trial`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        const data = await response.json();
        loading.close();

        if (data.success) {
          notifications.success('Trial Removed', '<%= t("admin.trial_removed_success") %>');
          setTimeout(() => window.location.reload(), 1000);
        } else {
          notifications.error('Failed to Remove Trial', data.error || 'Failed to remove trial access');
        }
      } catch (error) {
        loading.close();
        console.error('Error:', error);
        notifications.error('Network Error', 'Failed to remove trial access. Please check your connection.');
      }
    }
  }
</script>
