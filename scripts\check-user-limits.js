/**
 * Check specific user's limits and quota information
 */

const { db } = require('../db/database');
const Subscription = require('../models/Subscription');
const QuotaMiddleware = require('../middleware/quotaMiddleware');

async function checkUserLimits(username) {
  console.log(`🔍 Checking limits for user: ${username}\n`);

  try {
    // Get user from database
    const user = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM users WHERE username = ?',
        [username],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!user) {
      console.log('❌ User not found!');
      return;
    }

    console.log('👤 User Database Record:');
    console.log('   ID:', user.id);
    console.log('   Username:', user.username);
    console.log('   Plan Type:', user.plan_type);
    console.log('   Max Streaming Slots:', user.max_streaming_slots);
    console.log('   Max Storage GB:', user.max_storage_gb);
    console.log('   Used Storage GB:', user.used_storage_gb);

    // Get user's subscription
    const subscription = await Subscription.getUserSubscription(user.id);
    console.log('\n📋 Active Subscription:');
    if (subscription) {
      console.log('   Plan Name:', subscription.plan_name);
      console.log('   Status:', subscription.status);
      console.log('   End Date:', subscription.end_date);
      console.log('   Max Streaming Slots:', subscription.max_streaming_slots);
      console.log('   Max Storage GB:', subscription.max_storage_gb);
    } else {
      console.log('   No active subscription found');
    }

    // Get subscription including expired
    const expiredSubscription = await Subscription.getUserSubscriptionIncludingExpired(user.id);
    console.log('\n📋 Subscription (Including Expired):');
    if (expiredSubscription) {
      console.log('   Plan Name:', expiredSubscription.plan_name);
      console.log('   Status:', expiredSubscription.status);
      console.log('   End Date:', expiredSubscription.end_date);
      console.log('   Is Expired:', expiredSubscription.isExpired);
      console.log('   Max Streaming Slots:', expiredSubscription.max_streaming_slots);
      console.log('   Max Storage GB:', expiredSubscription.max_storage_gb);
    } else {
      console.log('   No subscription found');
    }

    // Check streaming slot limit
    const slotCheck = await Subscription.checkStreamingSlotLimit(user.id);
    console.log('\n🎯 Streaming Slot Check:');
    console.log('   Current Slots Used:', slotCheck.currentSlots);
    console.log('   Max Slots:', slotCheck.maxSlots);
    console.log('   Has Limit:', slotCheck.hasLimit);

    // Get quota info (what the UI sees)
    const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(user.id);
    console.log('\n📊 Quota Info (UI Data):');
    if (quotaInfo) {
      console.log('   Streaming Current:', quotaInfo.streaming.current);
      console.log('   Streaming Max:', quotaInfo.streaming.max);
      console.log('   Storage Current:', quotaInfo.storage.current, quotaInfo.storage.unit);
      console.log('   Storage Max:', quotaInfo.storage.max, quotaInfo.storage.unit);
      console.log('   Plan Name:', quotaInfo.plan.name);
      console.log('   Plan Price:', quotaInfo.plan.price);
    } else {
      console.log('   No quota info available');
    }

    // Check Preview plan configuration
    const previewPlan = await Subscription.getPlanByName('Preview');
    console.log('\n🔧 Preview Plan Configuration:');
    if (previewPlan) {
      console.log('   Name:', previewPlan.name);
      console.log('   Max Streaming Slots:', previewPlan.max_streaming_slots);
      console.log('   Max Storage GB:', previewPlan.max_storage_gb);
      console.log('   Price:', previewPlan.price);
    } else {
      console.log('   Preview plan not found!');
    }

    // Count user's streams
    const streamCount = await new Promise((resolve, reject) => {
      db.get(
        'SELECT COUNT(*) as count FROM streams WHERE user_id = ?',
        [user.id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        }
      );
    });

    console.log('\n📺 User Streams:');
    console.log('   Total Streams:', streamCount);

    // Analysis
    console.log('\n🔍 Analysis:');
    if (user.plan_type === 'Preview' && user.max_streaming_slots === 0) {
      console.log('   ✅ User is correctly set to Preview plan with 0 streaming slots');
    } else if (user.plan_type === 'Preview' && user.max_streaming_slots !== 0) {
      console.log('   ❌ User is on Preview plan but has incorrect streaming slots:', user.max_streaming_slots);
    } else {
      console.log('   ℹ️ User is on', user.plan_type, 'plan with', user.max_streaming_slots, 'streaming slots');
    }

    if (expiredSubscription && expiredSubscription.isExpired) {
      console.log('   ⚠️ User has expired subscription - should be on Preview plan');
    }

    if (quotaInfo && quotaInfo.streaming.max === 0) {
      console.log('   ✅ UI will show 0 streaming slots (correct for expired/Preview users)');
    } else if (quotaInfo && quotaInfo.streaming.max > 0) {
      console.log('   ❌ UI will show', quotaInfo.streaming.max, 'streaming slots (incorrect for expired users)');
    }

  } catch (error) {
    console.error('❌ Error checking user limits:', error);
  }
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: node scripts/check-user-limits.js <username>');
    process.exit(1);
  }

  const username = args[0];
  checkUserLimits(username).then(() => {
    console.log('\n✅ Check completed!');
    process.exit(0);
  }).catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = { checkUserLimits };
