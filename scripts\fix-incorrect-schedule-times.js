const Stream = require('../models/Stream');
const { convertToUTC, isValidTimezone, getDefaultTimezone } = require('../utils/timezone');

async function fixIncorrectScheduleTimes() {
  console.log('Starting script to fix incorrect schedule times...');

  try {
    const streamsToFix = await Stream.findAllWithIncorrectTimezone();

    if (streamsToFix.length === 0) {
      console.log('No streams with incorrect schedule times found.');
      return;
    }

    console.log(`Found ${streamsToFix.length} streams to fix.`);

    for (const stream of streamsToFix) {
      const { id, schedule_time, schedule_timezone } = stream;

      if (!schedule_time) {
        console.log(`Skipping stream ${id} because it has no schedule time.`);
        continue;
      }

      let timezone = schedule_timezone;
      if (!isValidTimezone(timezone)) {
        console.warn(`Stream ${id} has an invalid timezone: ${timezone}. Falling back to default.`);
        timezone = getDefaultTimezone();
      }

      const correctedUTCTime = convertToUTC(schedule_time, timezone);

      console.log(`Fixing stream ${id}:`);
      console.log(`  - Original schedule_time: ${schedule_time}`);
      console.log(`  - Original schedule_timezone: ${timezone}`);
      console.log(`  - Corrected UTC time: ${correctedUTCTime}`);

      await Stream.update(id, { schedule_time: correctedUTCTime });
    }

    console.log('Finished fixing incorrect schedule times.');
  } catch (error) {
    console.error('Error fixing incorrect schedule times:', error);
  }
}

fixIncorrectScheduleTimes();