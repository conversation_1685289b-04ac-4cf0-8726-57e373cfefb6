#!/usr/bin/env node

/**
 * Test script to verify admin subscription cancellation fix
 * This script tests that when an admin cancels a subscription:
 * 1. The subscription status is updated to 'cancelled'
 * 2. The end_date is set to current time
 * 3. The user is properly downgraded to Preview plan
 * 4. The user interface shows the subscription as cancelled/expired
 */

const { db } = require('./db/database');
const Subscription = require('./models/Subscription');
const User = require('./models/User');

async function testAdminCancellationFix() {
  console.log('🧪 Testing Admin Subscription Cancellation Fix...\n');

  try {
    // Step 1: Find a user with an active subscription (excluding Preview)
    const activeSubscription = await new Promise((resolve, reject) => {
      db.get(`
        SELECT us.*, u.username, sp.name as plan_name
        FROM user_subscriptions us
        JOIN users u ON us.user_id = u.id
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status = 'active' AND sp.name != 'Preview'
        ORDER BY us.created_at DESC
        LIMIT 1
      `, [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!activeSubscription) {
      console.log('❌ No active subscriptions found to test with');
      console.log('💡 Create a test subscription first or run this test when there are active subscriptions');
      return;
    }

    console.log('📋 Found test subscription:');
    console.log(`   User: ${activeSubscription.username} (ID: ${activeSubscription.user_id})`);
    console.log(`   Plan: ${activeSubscription.plan_name}`);
    console.log(`   Status: ${activeSubscription.status}`);
    console.log(`   End Date: ${activeSubscription.end_date}`);
    console.log(`   Subscription ID: ${activeSubscription.id}\n`);

    // Step 2: Test getUserSubscription before cancellation
    console.log('🔍 Testing getUserSubscription before cancellation...');
    const beforeCancellation = await Subscription.getUserSubscription(activeSubscription.user_id);
    console.log(`   Result: ${beforeCancellation ? `Active subscription found (${beforeCancellation.plan_name})` : 'No active subscription'}\n`);

    // Step 3: Test getUserSubscriptionIncludingExpired before cancellation
    console.log('🔍 Testing getUserSubscriptionIncludingExpired before cancellation...');
    const beforeCancellationIncluding = await Subscription.getUserSubscriptionIncludingExpired(activeSubscription.user_id);
    console.log(`   Result: ${beforeCancellationIncluding ? `Subscription found (${beforeCancellationIncluding.plan_name}, expired: ${beforeCancellationIncluding.isExpired}, cancelled: ${beforeCancellationIncluding.isCancelled})` : 'No subscription'}\n`);

    // Step 4: Simulate admin cancellation
    console.log('🛑 Simulating admin cancellation...');
    
    // Update subscription status to cancelled
    await Subscription.updateSubscriptionStatus(activeSubscription.id, 'cancelled');
    console.log('   ✅ Updated subscription status to cancelled');

    // Set end_date to current time
    const currentTime = new Date().toISOString();
    await Subscription.updateSubscriptionEndDate(activeSubscription.id, currentTime);
    console.log('   ✅ Updated end_date to current time');

    // Handle the cancellation as admin cancellation (keeps subscription record)
    const success = await Subscription.handleAdminCancellation(activeSubscription.user_id);
    console.log(`   ${success ? '✅' : '❌'} Handled admin cancellation (downgrade to Preview)\n`);

    // Step 5: Test getUserSubscription after cancellation
    console.log('🔍 Testing getUserSubscription after cancellation...');
    const afterCancellation = await Subscription.getUserSubscription(activeSubscription.user_id);
    console.log(`   Result: ${afterCancellation ? `Active subscription found (${afterCancellation.plan_name})` : 'No active subscription (EXPECTED)'}\n`);

    // Step 6: Test getUserSubscriptionIncludingExpired after cancellation
    console.log('🔍 Testing getUserSubscriptionIncludingExpired after cancellation...');
    const afterCancellationIncluding = await Subscription.getUserSubscriptionIncludingExpired(activeSubscription.user_id);
    console.log(`   Result: ${afterCancellationIncluding ? `Subscription found (${afterCancellationIncluding.plan_name}, expired: ${afterCancellationIncluding.isExpired}, cancelled: ${afterCancellationIncluding.isCancelled})` : 'No subscription'}\n`);

    // Step 7: Check user's current plan
    console.log('👤 Checking user\'s current plan...');
    const user = await User.findById(activeSubscription.user_id);
    if (user) {
      console.log(`   Current Plan: ${user.plan_type}`);
      console.log(`   Streaming Slots: ${user.max_streaming_slots}`);
      console.log(`   Storage: ${user.max_storage_gb}GB\n`);
    }

    // Step 8: Verify expected behavior
    console.log('✅ VERIFICATION RESULTS:');
    const tests = [
      {
        name: 'getUserSubscription returns null after cancellation',
        passed: afterCancellation === null,
        expected: 'null',
        actual: afterCancellation ? `${afterCancellation.plan_name}` : 'null'
      },
      {
        name: 'getUserSubscriptionIncludingExpired shows expired=true',
        passed: afterCancellationIncluding && afterCancellationIncluding.isExpired === true,
        expected: 'true',
        actual: afterCancellationIncluding ? afterCancellationIncluding.isExpired : 'null'
      },
      {
        name: 'getUserSubscriptionIncludingExpired shows cancelled=true',
        passed: afterCancellationIncluding && afterCancellationIncluding.isCancelled === true,
        expected: 'true',
        actual: afterCancellationIncluding ? afterCancellationIncluding.isCancelled : 'null'
      },
      {
        name: 'User downgraded to Preview plan',
        passed: user && user.plan_type === 'Preview',
        expected: 'Preview',
        actual: user ? user.plan_type : 'null'
      },
      {
        name: 'User has 0 streaming slots',
        passed: user && user.max_streaming_slots === 0,
        expected: '0',
        actual: user ? user.max_streaming_slots.toString() : 'null'
      }
    ];

    tests.forEach(test => {
      console.log(`   ${test.passed ? '✅' : '❌'} ${test.name}: Expected ${test.expected}, Got ${test.actual}`);
    });

    const allPassed = tests.every(test => test.passed);
    console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

    if (allPassed) {
      console.log('\n🎉 Admin cancellation fix is working correctly!');
      console.log('   - Cancelled subscriptions are treated as expired');
      console.log('   - Users are properly downgraded to Preview plan');
      console.log('   - UI will show subscription as cancelled instead of showing remaining days');
    } else {
      console.log('\n⚠️ Some issues detected. Please review the implementation.');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
if (require.main === module) {
  testAdminCancellationFix().then(() => {
    console.log('\n🏁 Test completed');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
}

module.exports = { testAdminCancellationFix };
